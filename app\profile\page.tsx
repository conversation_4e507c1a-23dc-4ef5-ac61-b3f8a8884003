"use client"

import { useState, useEffect } from "react"
import { Navigation } from "@/components/navigation"
import { ProtectedRoute } from "@/components/protected-route"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useGame } from "@/components/game-provider"
import { useAuth } from "@/components/auth-provider"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Bell,
  Shield,
  Download,
  Upload,
  Trophy,
  Target,
  Calendar,
  Clock,
  Brain,
  Star,
} from "lucide-react"

export default function ProfilePage() {
  const { userStats } = useGame()
  const { user } = useAuth()
  const [profile, setProfile] = useState({
    name: "",
    email: "",
    bio: "",
    institution: "",
    studyGoal: "3",
    notifications: {
      email: true,
      push: true,
      studyReminders: true,
      achievements: true,
    },
    preferences: {
      theme: "system",
      language: "en",
      difficulty: "medium",
      studyMode: "mixed",
    },
  })

  // Initialize profile with user data
  useEffect(() => {
    if (user) {
      setProfile(prev => ({
        ...prev,
        name: user.name || "",
        email: user.email || "",
        preferences: {
          ...prev.preferences,
          theme: user.preferences?.theme || "system",
        },
        notifications: {
          ...prev.notifications,
          email: user.preferences?.emailNotifications ?? true,
          studyReminders: user.preferences?.studyReminders ?? true,
        }
      }))
    }
  }, [user])

  const achievements = [
    { name: "First Steps", description: "Complete your first study session", earned: true, date: "2024-01-15" },
    { name: "Flashcard Master", description: "Review 100 flashcards", earned: true, date: "2024-01-20" },
    { name: "Quiz Hero", description: "Score 90% or higher on 5 quizzes", earned: true, date: "2024-01-25" },
    { name: "Week Warrior", description: "Study for 7 consecutive days", earned: false, date: null },
    { name: "Knowledge Seeker", description: "Upload 10 different PDFs", earned: false, date: null },
    { name: "Perfect Score", description: "Get 100% on any quiz", earned: false, date: null },
  ]

  const studyStats = [
    {
      label: "Total Study Time",
      value: user?.stats ? `${Math.floor(user.stats.totalStudyTime / 60)}h ${user.stats.totalStudyTime % 60}m` : "0h 0m",
      icon: Clock
    },
    {
      label: "Materials Uploaded",
      value: user?.stats?.documentsUploaded?.toString() || "0",
      icon: Upload
    },
    {
      label: "Flashcards Reviewed",
      value: user?.stats?.flashcardsReviewed?.toString() || "0",
      icon: Brain
    },
    {
      label: "Quizzes Completed",
      value: user?.stats?.quizzesCompleted?.toString() || "0",
      icon: Target
    },
    {
      label: "Average Score",
      value: "87%", // This could be calculated from quiz data
      icon: Trophy
    },
    {
      label: "Current Streak",
      value: `${userStats.streak} days`,
      icon: Calendar
    },
  ]

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navigation />

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Profile & Settings</h1>
          <p className="text-muted-foreground">Manage your account and customize your learning experience</p>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center gap-6">
                  <Avatar className="w-20 h-20">
                    <AvatarImage src="/placeholder.svg?height=80&width=80" />
                    <AvatarFallback>AJ</AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm">
                      Change Avatar
                    </Button>
                    <p className="text-sm text-muted-foreground">JPG, PNG or GIF (max 2MB)</p>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      value={profile.name}
                      onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="institution">Institution</Label>
                  <Input
                    id="institution"
                    value={profile.institution}
                    onChange={(e) => setProfile({ ...profile, institution: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="bio">Bio</Label>
                  <Textarea
                    id="bio"
                    value={profile.bio}
                    onChange={(e) => setProfile({ ...profile, bio: e.target.value })}
                    placeholder="Tell us about yourself..."
                  />
                </div>

                <div>
                  <Label htmlFor="goal">Daily Study Goal (hours)</Label>
                  <Select
                    value={profile.studyGoal}
                    onValueChange={(value) => setProfile({ ...profile, studyGoal: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 hour</SelectItem>
                      <SelectItem value="2">2 hours</SelectItem>
                      <SelectItem value="3">3 hours</SelectItem>
                      <SelectItem value="4">4 hours</SelectItem>
                      <SelectItem value="5">5+ hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button>Save Changes</Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {studyStats.map((stat, index) => {
                const Icon = stat.icon
                return (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <p className="text-2xl font-bold">{stat.value}</p>
                          <p className="text-sm text-muted-foreground">{stat.label}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Learning Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Level Progress</span>
                    <span className="text-sm text-muted-foreground">
                      Level {userStats.level} ({userStats.xp}/1000 XP)
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all"
                      style={{ width: `${(userStats.xp / 1000) * 100}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="achievements" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              {achievements.map((achievement, index) => (
                <Card key={index} className={achievement.earned ? "border-yellow-200 bg-yellow-50" : ""}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div
                        className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          achievement.earned ? "bg-yellow-100 text-yellow-600" : "bg-muted text-muted-foreground"
                        }`}
                      >
                        <Star className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold mb-1">{achievement.name}</h3>
                        <p className="text-sm text-muted-foreground mb-2">{achievement.description}</p>
                        {achievement.earned ? (
                          <Badge variant="secondary">Earned on {achievement.date}</Badge>
                        ) : (
                          <Badge variant="outline">Not earned yet</Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notifications
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Email Notifications</p>
                    <p className="text-sm text-muted-foreground">Receive updates via email</p>
                  </div>
                  <Switch
                    checked={profile.notifications.email}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, email: checked },
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Push Notifications</p>
                    <p className="text-sm text-muted-foreground">Receive browser notifications</p>
                  </div>
                  <Switch
                    checked={profile.notifications.push}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, push: checked },
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Study Reminders</p>
                    <p className="text-sm text-muted-foreground">Daily study session reminders</p>
                  </div>
                  <Switch
                    checked={profile.notifications.studyReminders}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, studyReminders: checked },
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Achievement Notifications</p>
                    <p className="text-sm text-muted-foreground">Get notified when you earn badges</p>
                  </div>
                  <Switch
                    checked={profile.notifications.achievements}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, achievements: checked },
                      })
                    }
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Preferences
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Theme</Label>
                  <Select
                    value={profile.preferences.theme}
                    onValueChange={(value) =>
                      setProfile({
                        ...profile,
                        preferences: { ...profile.preferences, theme: value },
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Default Difficulty</Label>
                  <Select
                    value={profile.preferences.difficulty}
                    onValueChange={(value) =>
                      setProfile({
                        ...profile,
                        preferences: { ...profile.preferences, difficulty: value },
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Study Mode</Label>
                  <Select
                    value={profile.preferences.studyMode}
                    onValueChange={(value) =>
                      setProfile({
                        ...profile,
                        preferences: { ...profile.preferences, studyMode: value },
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="flashcards">Flashcards Only</SelectItem>
                      <SelectItem value="quizzes">Quizzes Only</SelectItem>
                      <SelectItem value="mixed">Mixed Mode</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Data Export
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Export your study data, progress, and materials for backup or transfer to another platform.
                </p>
                <div className="flex gap-2">
                  <Button variant="outline">Export Study Data</Button>
                  <Button variant="outline">Export Flashcards</Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <Shield className="h-5 w-5" />
                  Danger Zone
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                  <h4 className="font-medium text-red-800 mb-2">Delete Account</h4>
                  <p className="text-sm text-red-600 mb-3">
                    This action cannot be undone. All your data will be permanently deleted.
                  </p>
                  <Button variant="destructive" size="sm">
                    Delete Account
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </ProtectedRoute>
  )
}
