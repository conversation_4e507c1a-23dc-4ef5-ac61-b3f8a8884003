import { MongoClient, Db } from 'mongodb'
import mongoose from 'mongoose'

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/learnify'

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local')
}

// MongoDB native client connection
let client: MongoClient
let clientPromise: Promise<MongoClient>

declare global {
  var _mongoClientPromise: Promise<MongoClient> | undefined
}

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  if (!global._mongoClientPromise) {
    client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
    })
    global._mongoClientPromise = client.connect()
  }
  clientPromise = global._mongoClientPromise
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(MONGODB_URI, {
    serverSelectionTimeoutMS: 5000,
  })
  clientPromise = client.connect()
}

// Export a module-scoped MongoClient promise. By doing this in a
// separate module, the client can be shared across functions.
export { clientPromise }

// Get the database instance
export async function getDatabase(): Promise<Db> {
  const client = await clientPromise
  return client.db('learnify')
}

// Mongoose connection
let isConnected = false

export async function connectToDatabase() {
  if (isConnected) {
    console.log('Already connected to MongoDB')
    return mongoose.connection
  }

  try {
    console.log('Connecting to MongoDB with Mongoose...')
    await mongoose.connect(MONGODB_URI, {
      bufferCommands: false,
    })
    
    isConnected = true
    console.log('Connected to MongoDB with Mongoose')
    return mongoose.connection
  } catch (error) {
    console.error('Error connecting to MongoDB:', error)
    throw error
  }
}

// Disconnect from database
export async function disconnectFromDatabase() {
  if (!isConnected) {
    return
  }

  try {
    await mongoose.disconnect()
    isConnected = false
    console.log('Disconnected from MongoDB')
  } catch (error) {
    console.error('Error disconnecting from MongoDB:', error)
    throw error
  }
}

// Database health check
export async function checkDatabaseHealth() {
  try {
    const db = await getDatabase()
    const adminDb = db.admin()
    const result = await adminDb.ping()
    return { success: true, result }
  } catch (error) {
    console.error('Database health check failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}
