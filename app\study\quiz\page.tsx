"use client"

import { useState, useEffect } from "react"
import { Navigation } from "@/components/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useGame } from "@/components/game-provider"
import { CheckCircle, XCircle, Clock, Trophy, Target, Brain } from "lucide-react"
import Link from "next/link"

interface Question {
  id: string
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
  difficulty: "easy" | "medium" | "hard"
  topic: string
}

const sampleQuestions: Question[] = [
  {
    id: "1",
    question: "Which organelle is responsible for protein synthesis?",
    options: ["Mitochondria", "Ribosomes", "Nucleus", "Golgi apparatus"],
    correctAnswer: 1,
    explanation: "Ribosomes are the cellular structures responsible for protein synthesis by translating mRNA.",
    difficulty: "medium",
    topic: "Cell Biology",
  },
  {
    id: "2",
    question: "What is the main function of chloroplasts?",
    options: ["Cellular respiration", "Photosynthesis", "Protein synthesis", "Waste removal"],
    correctAnswer: 1,
    explanation: "Chloroplasts contain chlorophyll and are responsible for photosynthesis in plant cells.",
    difficulty: "easy",
    topic: "Plant Biology",
  },
  {
    id: "3",
    question: "Which process occurs during the S phase of the cell cycle?",
    options: ["Cell division", "DNA replication", "Protein synthesis", "Cell growth"],
    correctAnswer: 1,
    explanation: "The S (synthesis) phase is when DNA replication occurs, doubling the genetic material.",
    difficulty: "hard",
    topic: "Cell Division",
  },
  {
    id: "4",
    question: "What is the function of the cell membrane?",
    options: ["Energy production", "Genetic storage", "Selective permeability", "Protein synthesis"],
    correctAnswer: 2,
    explanation: "The cell membrane controls what enters and exits the cell through selective permeability.",
    difficulty: "medium",
    topic: "Cell Biology",
  },
  {
    id: "5",
    question: "Which molecule stores genetic information?",
    options: ["RNA", "DNA", "Protein", "Lipid"],
    correctAnswer: 1,
    explanation: "DNA (deoxyribonucleic acid) stores genetic information in all living organisms.",
    difficulty: "easy",
    topic: "Molecular Biology",
  },
]

export default function QuizPage() {
  const { addXP } = useGame()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState<string>("")
  const [showResult, setShowResult] = useState(false)
  const [answers, setAnswers] = useState<number[]>([])
  const [score, setScore] = useState(0)
  const [timeLeft, setTimeLeft] = useState(30)
  const [quizStarted, setQuizStarted] = useState(false)
  const [quizCompleted, setQuizCompleted] = useState(false)

  useEffect(() => {
    if (quizStarted && timeLeft > 0 && !showResult && !quizCompleted) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    } else if (timeLeft === 0 && !showResult) {
      handleNextQuestion()
    }
  }, [timeLeft, quizStarted, showResult, quizCompleted])

  const startQuiz = () => {
    setQuizStarted(true)
    setCurrentQuestion(0)
    setScore(0)
    setAnswers([])
    setTimeLeft(30)
    setQuizCompleted(false)
  }

  const handleNextQuestion = () => {
    const answerIndex = selectedAnswer ? Number.parseInt(selectedAnswer) : -1
    const newAnswers = [...answers, answerIndex]
    setAnswers(newAnswers)

    if (answerIndex === sampleQuestions[currentQuestion].correctAnswer) {
      setScore(score + 1)
      addXP(20)
    }

    if (currentQuestion < sampleQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setSelectedAnswer("")
      setTimeLeft(30)
    } else {
      setQuizCompleted(true)
      setShowResult(true)
    }
  }

  const resetQuiz = () => {
    setQuizStarted(false)
    setCurrentQuestion(0)
    setSelectedAnswer("")
    setShowResult(false)
    setScore(0)
    setTimeLeft(30)
    setAnswers([])
    setQuizCompleted(false)
  }

  if (!quizStarted) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">Biology Quiz</h1>
            <p className="text-muted-foreground">Test your knowledge of cell biology and molecular processes</p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Quiz Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Questions:</span>
                  <Badge>{sampleQuestions.length}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Time per question:</span>
                  <Badge variant="secondary">30 seconds</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Difficulty:</span>
                  <Badge variant="outline">Mixed</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">XP per correct:</span>
                  <Badge className="bg-green-100 text-green-800">+20 XP</Badge>
                </div>
                <Button onClick={startQuiz} className="w-full">
                  Start Quiz
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Topics Covered
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Cell Biology</span>
                    <Badge variant="secondary">60%</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Molecular Biology</span>
                    <Badge variant="secondary">20%</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Plant Biology</span>
                    <Badge variant="secondary">20%</Badge>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    This quiz covers key concepts from your uploaded study materials on cell structure and function.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  if (showResult) {
    const percentage = Math.round((score / sampleQuestions.length) * 100)
    const grade =
      percentage >= 90 ? "A" : percentage >= 80 ? "B" : percentage >= 70 ? "C" : percentage >= 60 ? "D" : "F"

    return (
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <Card className="text-center mb-6">
            <CardHeader>
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="h-8 w-8 text-primary" />
              </div>
              <CardTitle className="text-2xl">Quiz Complete!</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">{percentage}%</div>
                  <div className="text-sm text-muted-foreground">Score</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{grade}</div>
                  <div className="text-sm text-muted-foreground">Grade</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{score * 20}</div>
                  <div className="text-sm text-muted-foreground">XP Earned</div>
                </div>
              </div>

              <div className="text-lg text-muted-foreground">
                You answered {score} out of {sampleQuestions.length} questions correctly
              </div>

              <div className="flex gap-4 justify-center">
                <Button onClick={resetQuiz}>Retake Quiz</Button>
                <Link href="/study/dashboard">
                  <Button variant="outline">Back to Dashboard</Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Review Answers */}
          <Card>
            <CardHeader>
              <CardTitle>Review Your Answers</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {sampleQuestions.map((question, index) => (
                <div key={question.id} className="border rounded-lg p-4">
                  <div className="flex items-start gap-3 mb-3">
                    {answers[index] === question.correctAnswer ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                    )}
                    <div className="flex-1">
                      <p className="font-medium mb-2">
                        {index + 1}. {question.question}
                      </p>
                      <div className="space-y-1 text-sm">
                        <p className="text-green-600">✓ Correct: {question.options[question.correctAnswer]}</p>
                        {answers[index] !== question.correctAnswer && answers[index] !== -1 && (
                          <p className="text-red-600">✗ Your answer: {question.options[answers[index]]}</p>
                        )}
                        {answers[index] === -1 && <p className="text-gray-600">⏱ No answer (time expired)</p>}
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">{question.explanation}</p>
                    </div>
                    <Badge variant="outline">{question.difficulty}</Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const question = sampleQuestions[currentQuestion]
  const progress = ((currentQuestion + 1) / sampleQuestions.length) * 100

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Biology Quiz</h1>
            <p className="text-muted-foreground">
              Question {currentQuestion + 1} of {sampleQuestions.length}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span className={`font-mono text-lg ${timeLeft <= 10 ? "text-red-500" : ""}`}>{timeLeft}s</span>
          </div>
        </div>

        {/* Progress */}
        <div className="mb-6">
          <Progress value={progress} />
          <div className="flex justify-between items-center mt-2">
            <Badge variant="secondary">{question.topic}</Badge>
            <Badge variant="outline">{question.difficulty}</Badge>
          </div>
        </div>

        {/* Question */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{question.question}</CardTitle>
          </CardHeader>
          <CardContent>
            <RadioGroup value={selectedAnswer} onValueChange={setSelectedAnswer}>
              <div className="space-y-3">
                {question.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                    <Label
                      htmlFor={`option-${index}`}
                      className="flex-1 cursor-pointer p-3 rounded-lg border hover:bg-accent"
                    >
                      {option}
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>

            <div className="flex gap-4 mt-6">
              <Button onClick={handleNextQuestion} disabled={!selectedAnswer} className="flex-1">
                {currentQuestion < sampleQuestions.length - 1 ? "Next Question" : "Finish Quiz"}
              </Button>
              <Button variant="outline" onClick={resetQuiz}>
                Quit Quiz
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
