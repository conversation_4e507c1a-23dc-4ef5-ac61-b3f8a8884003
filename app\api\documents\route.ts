import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Document } from '@/models'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const tags = searchParams.get('tags')?.split(',').filter(Boolean)
    const search = searchParams.get('search')
    const subject = searchParams.get('subject')
    const difficulty = searchParams.get('difficulty')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = parseInt(searchParams.get('skip') || '0')
    
    let query: any = {}
    
    if (userId) {
      query.userId = userId
    }
    
    if (subject) {
      query.subject = subject
    }
    
    if (difficulty) {
      query.difficulty = difficulty
    }
    
    if (tags && tags.length > 0) {
      query.tags = { $in: tags.map(tag => tag.toLowerCase()) }
    }
    
    let documents
    
    if (search) {
      // Text search
      query.$text = { $search: search }
      documents = await Document.find(query, { score: { $meta: 'textScore' } })
        .sort({ score: { $meta: 'textScore' } })
        .limit(limit)
        .skip(skip)
    } else {
      // Regular query
      documents = await Document.find(query)
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
    }
    
    const total = await Document.countDocuments(query)
    
    return NextResponse.json({
      success: true,
      data: documents,
      pagination: {
        total,
        limit,
        skip,
        hasMore: skip + limit < total
      }
    })
    
  } catch (error) {
    console.error('Error fetching documents:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch documents' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const {
      userId,
      title,
      filename,
      fileUrl,
      fileSize,
      mimeType,
      content,
      extractedText,
      summary,
      tags,
      subject,
      difficulty,
      metadata
    } = body
    
    // Validate required fields
    if (!userId || !title || !filename || !fileUrl || !fileSize || !mimeType || !subject) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    // Create new document
    const documentData = {
      userId,
      title,
      filename,
      fileUrl,
      fileSize,
      mimeType,
      content: content || '',
      extractedText: extractedText || '',
      summary: summary || '',
      tags: tags || [],
      subject,
      difficulty: difficulty || 'medium',
      metadata: metadata || {}
    }
    
    const document = new Document(documentData)
    await document.save()
    
    return NextResponse.json({
      success: true,
      message: 'Document created successfully',
      data: document
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error creating document:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to create document' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Document ID is required' },
        { status: 400 }
      )
    }
    
    const document = await Document.findById(id)
    if (!document) {
      return NextResponse.json(
        { success: false, message: 'Document not found' },
        { status: 404 }
      )
    }
    
    // Update document fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        document[key] = updateData[key]
      }
    })
    
    await document.save()
    
    return NextResponse.json({
      success: true,
      message: 'Document updated successfully',
      data: document
    })
    
  } catch (error) {
    console.error('Error updating document:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to update document' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Document ID is required' },
        { status: 400 }
      )
    }
    
    const document = await Document.findById(id)
    if (!document) {
      return NextResponse.json(
        { success: false, message: 'Document not found' },
        { status: 404 }
      )
    }
    
    await Document.deleteOne({ _id: id })
    
    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    })
    
  } catch (error) {
    console.error('Error deleting document:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to delete document' },
      { status: 500 }
    )
  }
}
