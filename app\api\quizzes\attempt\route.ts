import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Quiz } from '@/models'
import { randomUUID } from 'crypto'

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { quizId, action, attemptId, questionIndex, answerIndex, timeSpent } = body
    
    if (!quizId) {
      return NextResponse.json(
        { success: false, message: 'Quiz ID is required' },
        { status: 400 }
      )
    }
    
    const quiz = await Quiz.findById(quizId)
    if (!quiz) {
      return NextResponse.json(
        { success: false, message: 'Quiz not found' },
        { status: 404 }
      )
    }
    
    switch (action) {
      case 'start':
        // Start a new attempt
        const newAttemptId = randomUUID()
        await quiz.startAttempt(newAttemptId)
        
        return NextResponse.json({
          success: true,
          message: 'Quiz attempt started',
          data: {
            attemptId: newAttemptId,
            quiz: {
              id: quiz._id,
              title: quiz.title,
              description: quiz.description,
              questions: quiz.questions.map(q => ({
                question: q.question,
                options: q.options,
                difficulty: q.difficulty
              })),
              timeLimit: quiz.timeLimit,
              settings: quiz.settings
            }
          }
        })
      
      case 'answer':
        // Submit an answer
        if (!attemptId || typeof questionIndex !== 'number' || typeof answerIndex !== 'number') {
          return NextResponse.json(
            { success: false, message: 'Attempt ID, question index, and answer index are required' },
            { status: 400 }
          )
        }
        
        try {
          await quiz.submitAnswer(attemptId, questionIndex, answerIndex)
          
          return NextResponse.json({
            success: true,
            message: 'Answer submitted successfully'
          })
        } catch (error) {
          return NextResponse.json(
            { success: false, message: error.message },
            { status: 400 }
          )
        }
      
      case 'complete':
        // Complete the attempt
        if (!attemptId || typeof timeSpent !== 'number') {
          return NextResponse.json(
            { success: false, message: 'Attempt ID and time spent are required' },
            { status: 400 }
          )
        }
        
        try {
          await quiz.completeAttempt(attemptId, timeSpent)
          const attempt = quiz.getAttempt(attemptId)
          
          // Prepare results with correct answers if settings allow
          const results = {
            score: attempt.score,
            timeSpent: attempt.timeSpent,
            completedAt: attempt.completedAt,
            answers: attempt.answers
          }
          
          if (quiz.settings.showCorrectAnswers) {
            results.correctAnswers = quiz.questions.map(q => q.correctAnswer)
            results.explanations = quiz.questions.map(q => q.explanation)
          }
          
          return NextResponse.json({
            success: true,
            message: 'Quiz completed successfully',
            data: results
          })
        } catch (error) {
          return NextResponse.json(
            { success: false, message: error.message },
            { status: 400 }
          )
        }
      
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        )
    }
    
  } catch (error) {
    console.error('Error handling quiz attempt:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to handle quiz attempt' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const quizId = searchParams.get('quizId')
    const attemptId = searchParams.get('attemptId')
    
    if (!quizId) {
      return NextResponse.json(
        { success: false, message: 'Quiz ID is required' },
        { status: 400 }
      )
    }
    
    const quiz = await Quiz.findById(quizId)
    if (!quiz) {
      return NextResponse.json(
        { success: false, message: 'Quiz not found' },
        { status: 404 }
      )
    }
    
    if (attemptId) {
      // Get specific attempt
      const attempt = quiz.getAttempt(attemptId)
      if (!attempt) {
        return NextResponse.json(
          { success: false, message: 'Attempt not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json({
        success: true,
        data: attempt
      })
    } else {
      // Get all attempts for the quiz
      return NextResponse.json({
        success: true,
        data: {
          attempts: quiz.attempts,
          stats: {
            totalAttempts: quiz.totalAttempts,
            averageScore: quiz.averageScore,
            bestScore: quiz.bestScore,
            completionRate: quiz.completionRate
          }
        }
      })
    }
    
  } catch (error) {
    console.error('Error fetching quiz attempt data:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch attempt data' },
      { status: 500 }
    )
  }
}
