import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { User } from '@/models'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = parseInt(searchParams.get('skip') || '0')
    
    if (email) {
      // Find user by email
      const user = await User.findByEmail(email)
      if (!user) {
        return NextResponse.json(
          { success: false, message: 'User not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json({
        success: true,
        data: user.profile
      })
    }
    
    // Get all users with pagination
    const users = await User.find()
      .select('name email avatar stats createdAt')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip)
    
    const total = await User.countDocuments()
    
    return NextResponse.json({
      success: true,
      data: users,
      pagination: {
        total,
        limit,
        skip,
        hasMore: skip + limit < total
      }
    })
    
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { email, name, avatar, preferences } = body
    
    // Validate required fields
    if (!email || !name) {
      return NextResponse.json(
        { success: false, message: 'Email and name are required' },
        { status: 400 }
      )
    }
    
    // Check if user already exists
    const existingUser = await User.findByEmail(email)
    if (existingUser) {
      return NextResponse.json(
        { success: false, message: 'User with this email already exists' },
        { status: 409 }
      )
    }
    
    // Create new user
    const userData = {
      email,
      name,
      avatar: avatar || null,
      preferences: preferences || {}
    }
    
    const user = new User(userData)
    await user.save()
    
    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      data: user.profile
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error creating user:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to create user' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { email, name, avatar, preferences, stats } = body
    
    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      )
    }
    
    const user = await User.findByEmail(email)
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      )
    }
    
    // Update user fields
    if (name) user.name = name
    if (avatar !== undefined) user.avatar = avatar
    if (preferences) {
      user.preferences = { ...user.preferences, ...preferences }
    }
    if (stats) {
      user.stats = { ...user.stats, ...stats }
    }
    
    await user.save()
    
    return NextResponse.json({
      success: true,
      message: 'User updated successfully',
      data: user.profile
    })
    
  } catch (error) {
    console.error('Error updating user:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    
    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      )
    }
    
    const user = await User.findByEmail(email)
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      )
    }
    
    await User.deleteOne({ email })
    
    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    })
    
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
