import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { User, Document, Flashcard, Quiz } from '@/models'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const type = searchParams.get('type') || 'overview'
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      )
    }
    
    const user = await User.findById(userId)
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      )
    }
    
    let stats = {}
    
    switch (type) {
      case 'overview':
        // Get overview statistics
        const [documentCount, flashcardStats, quizStats] = await Promise.all([
          Document.countDocuments({ userId, isProcessed: true }),
          Flashcard.getStudyStats(userId),
          Quiz.getQuizStats(userId)
        ])
        
        stats = {
          user: {
            name: user.name,
            email: user.email,
            joinedAt: user.createdAt,
            stats: user.stats
          },
          documents: {
            total: documentCount,
            uploaded: user.stats.documentsUploaded
          },
          flashcards: flashcardStats[0] || {
            totalCards: 0,
            dueCards: 0,
            averageSuccessRate: 0
          },
          quizzes: quizStats[0] || {
            totalQuizzes: 0,
            totalAttempts: 0,
            averageScore: 0,
            bestScore: 0,
            totalTimeSpent: 0
          }
        }
        break
      
      case 'documents':
        // Get document statistics
        const documentStats = await Document.aggregate([
          { $match: { userId } },
          {
            $group: {
              _id: null,
              totalDocuments: { $sum: 1 },
              totalSize: { $sum: '$fileSize' },
              processedDocuments: {
                $sum: { $cond: ['$isProcessed', 1, 0] }
              },
              subjectBreakdown: {
                $push: '$subject'
              },
              difficultyBreakdown: {
                $push: '$difficulty'
              }
            }
          }
        ])
        
        const subjectCounts = {}
        const difficultyCounts = {}
        
        if (documentStats[0]) {
          documentStats[0].subjectBreakdown.forEach(subject => {
            subjectCounts[subject] = (subjectCounts[subject] || 0) + 1
          })
          
          documentStats[0].difficultyBreakdown.forEach(difficulty => {
            difficultyCounts[difficulty] = (difficultyCounts[difficulty] || 0) + 1
          })
        }
        
        stats = {
          total: documentStats[0]?.totalDocuments || 0,
          processed: documentStats[0]?.processedDocuments || 0,
          totalSize: documentStats[0]?.totalSize || 0,
          subjects: subjectCounts,
          difficulties: difficultyCounts
        }
        break
      
      case 'flashcards':
        // Get detailed flashcard statistics
        const flashcardDetailedStats = await Flashcard.aggregate([
          { $match: { userId, isActive: true } },
          {
            $group: {
              _id: null,
              totalCards: { $sum: 1 },
              totalReviews: { $sum: '$reviewCount' },
              totalCorrect: { $sum: '$correctCount' },
              dueCards: {
                $sum: {
                  $cond: [{ $lte: ['$nextReviewAt', new Date()] }, 1, 0]
                }
              },
              difficultyBreakdown: {
                $push: '$difficulty'
              },
              averageInterval: { $avg: '$spacedRepetition.interval' },
              averageEaseFactor: { $avg: '$spacedRepetition.easeFactor' }
            }
          }
        ])
        
        const flashcardDifficultyCounts = {}
        if (flashcardDetailedStats[0]) {
          flashcardDetailedStats[0].difficultyBreakdown.forEach(difficulty => {
            flashcardDifficultyCounts[difficulty] = (flashcardDifficultyCounts[difficulty] || 0) + 1
          })
        }
        
        stats = {
          total: flashcardDetailedStats[0]?.totalCards || 0,
          due: flashcardDetailedStats[0]?.dueCards || 0,
          totalReviews: flashcardDetailedStats[0]?.totalReviews || 0,
          totalCorrect: flashcardDetailedStats[0]?.totalCorrect || 0,
          successRate: flashcardDetailedStats[0]?.totalReviews > 0 
            ? Math.round((flashcardDetailedStats[0].totalCorrect / flashcardDetailedStats[0].totalReviews) * 100)
            : 0,
          averageInterval: Math.round(flashcardDetailedStats[0]?.averageInterval || 0),
          averageEaseFactor: Math.round((flashcardDetailedStats[0]?.averageEaseFactor || 0) * 100) / 100,
          difficulties: flashcardDifficultyCounts
        }
        break
      
      case 'quizzes':
        // Get detailed quiz statistics
        const quizDetailedStats = await Quiz.aggregate([
          { $match: { userId, isActive: true } },
          { $unwind: { path: '$attempts', preserveNullAndEmptyArrays: true } },
          {
            $group: {
              _id: null,
              totalQuizzes: { $addToSet: '$_id' },
              totalAttempts: {
                $sum: { $cond: [{ $ne: ['$attempts', null] }, 1, 0] }
              },
              completedAttempts: {
                $sum: { $cond: ['$attempts.isCompleted', 1, 0] }
              },
              totalScore: {
                $sum: { $cond: ['$attempts.isCompleted', '$attempts.score', 0] }
              },
              bestScore: {
                $max: { $cond: ['$attempts.isCompleted', '$attempts.score', 0] }
              },
              totalTimeSpent: {
                $sum: { $cond: ['$attempts.isCompleted', '$attempts.timeSpent', 0] }
              },
              difficultyBreakdown: { $push: '$difficulty' }
            }
          },
          {
            $project: {
              totalQuizzes: { $size: '$totalQuizzes' },
              totalAttempts: 1,
              completedAttempts: 1,
              averageScore: {
                $cond: [
                  { $gt: ['$completedAttempts', 0] },
                  { $divide: ['$totalScore', '$completedAttempts'] },
                  0
                ]
              },
              bestScore: 1,
              totalTimeSpent: 1,
              difficultyBreakdown: 1
            }
          }
        ])
        
        const quizDifficultyCounts = {}
        if (quizDetailedStats[0]) {
          quizDetailedStats[0].difficultyBreakdown.forEach(difficulty => {
            if (difficulty) {
              quizDifficultyCounts[difficulty] = (quizDifficultyCounts[difficulty] || 0) + 1
            }
          })
        }
        
        stats = {
          totalQuizzes: quizDetailedStats[0]?.totalQuizzes || 0,
          totalAttempts: quizDetailedStats[0]?.totalAttempts || 0,
          completedAttempts: quizDetailedStats[0]?.completedAttempts || 0,
          averageScore: Math.round(quizDetailedStats[0]?.averageScore || 0),
          bestScore: quizDetailedStats[0]?.bestScore || 0,
          totalTimeSpent: quizDetailedStats[0]?.totalTimeSpent || 0,
          completionRate: quizDetailedStats[0]?.totalAttempts > 0
            ? Math.round((quizDetailedStats[0].completedAttempts / quizDetailedStats[0].totalAttempts) * 100)
            : 0,
          difficulties: quizDifficultyCounts
        }
        break
      
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid stats type' },
          { status: 400 }
        )
    }
    
    return NextResponse.json({
      success: true,
      data: stats,
      type,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Error fetching stats:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch stats' },
      { status: 500 }
    )
  }
}
