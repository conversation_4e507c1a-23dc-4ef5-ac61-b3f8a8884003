#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the Learnify API endpoints
 */

const { MongoClient } = require('mongodb')
const mongoose = require('mongoose')

async function testAPI() {
  console.log('🧪 Testing Learnify Database and Models...\n')

  const MONGODB_URI = 'mongodb://localhost:27017/learnify'
  let client

  try {
    // Test 1: Database Connection
    console.log('1️⃣ Testing database connection...')
    client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
    })
    await client.connect()
    console.log('   ✅ Database connected successfully!')

    // Test 2: Mongoose Connection
    console.log('\n2️⃣ Testing Mongoose connection...')
    await mongoose.connect(MONGODB_URI, {
      bufferCommands: false,
    })
    console.log('   ✅ Mongoose connected successfully!')

    // Test 3: Database Collections
    console.log('\n3️⃣ Testing database collections...')
    const db = client.db('learnify')
    const collections = await db.listCollections().toArray()
    console.log('   ✅ Found collections:', collections.map(c => c.name).join(', '))

    // Test collection counts
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments()
      console.log(`   📊 ${collection.name}: ${count} documents`)
    }

    // Test 4: Sample Data Verification
    console.log('\n4️⃣ Testing sample data...')
    const sampleUser = await db.collection('users').findOne({ email: '<EMAIL>' })
    if (sampleUser) {
      console.log('   ✅ Sample user found:', sampleUser.name)
    } else {
      console.log('   ⚠️ Sample user not found')
    }

    const sampleDoc = await db.collection('documents').findOne({ title: 'Introduction to Machine Learning' })
    if (sampleDoc) {
      console.log('   ✅ Sample document found:', sampleDoc.title)
    } else {
      console.log('   ⚠️ Sample document not found')
    }

    // Test 5: Database Indexes
    console.log('\n5️⃣ Testing database indexes...')
    const userIndexes = await db.collection('users').indexes()
    console.log('   ✅ User indexes:', userIndexes.length)

    const docIndexes = await db.collection('documents').indexes()
    console.log('   ✅ Document indexes:', docIndexes.length)

    const flashcardIndexes = await db.collection('flashcards').indexes()
    console.log('   ✅ Flashcard indexes:', flashcardIndexes.length)

    const quizIndexes = await db.collection('quizzes').indexes()
    console.log('   ✅ Quiz indexes:', quizIndexes.length)

    console.log('\n🎉 All database components tested successfully!')
    console.log('\n📋 Test Summary:')
    console.log('   ✅ MongoDB native connection')
    console.log('   ✅ Mongoose connection')
    console.log('   ✅ Database collections verified')
    console.log('   ✅ Sample data verified')
    console.log('   ✅ Database indexes verified')

    return { success: true }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message)
    console.error('Stack trace:', error.stack)
    return { success: false, error: error.message }
  } finally {
    // Cleanup connections
    if (client) {
      await client.close()
      console.log('\n🔌 MongoDB connection closed')
    }
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect()
      console.log('🔌 Mongoose connection closed')
    }
  }
}

// Run the test
if (require.main === module) {
  testAPI()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ All tests passed!')
        process.exit(0)
      } else {
        console.log('\n❌ Tests failed!')
        process.exit(1)
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error)
      process.exit(1)
    })
}

module.exports = { testAPI }
