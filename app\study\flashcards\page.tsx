"use client"

import { useState } from "react"
import { Navigation } from "@/components/navigation"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { FlashcardComponent } from "@/components/flashcard"
import { useGame } from "@/components/game-provider"
import {
  RotateCcw,
  CheckCircle,
  XCircle,
  Brain,
  Target,
  Clock,
  ArrowLeft,
  ArrowRight,
  Shuffle,
  Settings,
} from "lucide-react"
import Link from "next/link"

interface Flashcard {
  id: string
  front: string
  back: string
  difficulty: "easy" | "medium" | "hard"
  topic: string
  mastered: boolean
}

const sampleFlashcards: Flashcard[] = [
  {
    id: "1",
    front: "What is the powerhouse of the cell?",
    back: "Mitochondria - responsible for producing ATP (energy) through cellular respiration",
    difficulty: "easy",
    topic: "Cell Biology",
    mastered: false,
  },
  {
    id: "2",
    front: "Define photosynthesis",
    back: "The process by which plants convert light energy, carbon dioxide, and water into glucose and oxygen",
    difficulty: "medium",
    topic: "Plant Biology",
    mastered: false,
  },
  {
    id: "3",
    front: "What is DNA replication?",
    back: "The process of copying DNA molecules to produce two identical DNA molecules from one original molecule",
    difficulty: "hard",
    topic: "Molecular Biology",
    mastered: true,
  },
  {
    id: "4",
    front: "What are ribosomes?",
    back: "Small structures in cells responsible for protein synthesis by translating mRNA into proteins",
    difficulty: "medium",
    topic: "Cell Biology",
    mastered: false,
  },
  {
    id: "5",
    front: "Define osmosis",
    back: "The movement of water molecules through a semipermeable membrane from high to low concentration",
    difficulty: "easy",
    topic: "Cell Biology",
    mastered: false,
  },
]

export default function FlashcardsPage() {
  const { addXP } = useGame()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isFlipped, setIsFlipped] = useState(false)
  const [correctCount, setCorrectCount] = useState(0)
  const [incorrectCount, setIncorrectCount] = useState(0)
  const [sessionStartTime] = useState(Date.now())
  const [studyMode, setStudyMode] = useState<"all" | "unmastered">("unmastered")

  const filteredCards = studyMode === "all" ? sampleFlashcards : sampleFlashcards.filter((card) => !card.mastered)
  const currentCard = filteredCards[currentIndex]
  const progress = ((currentIndex + 1) / filteredCards.length) * 100

  const handleNext = () => {
    setIsFlipped(false)
    if (currentIndex < filteredCards.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const handlePrevious = () => {
    setIsFlipped(false)
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const handleCorrect = () => {
    setCorrectCount(correctCount + 1)
    addXP(10)
    handleNext()
  }

  const handleIncorrect = () => {
    setIncorrectCount(incorrectCount + 1)
    handleNext()
  }

  const shuffleCards = () => {
    // In a real app, this would shuffle the cards array
    setCurrentIndex(0)
    setIsFlipped(false)
  }

  const resetSession = () => {
    setCurrentIndex(0)
    setIsFlipped(false)
    setCorrectCount(0)
    setIncorrectCount(0)
  }

  const getSessionTime = () => {
    const elapsed = Date.now() - sessionStartTime
    const minutes = Math.floor(elapsed / 60000)
    const seconds = Math.floor((elapsed % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  if (currentIndex >= filteredCards.length) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <Card className="text-center">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold mb-4">Session Complete!</h2>
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{correctCount}</div>
                  <div className="text-sm text-muted-foreground">Correct</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{incorrectCount}</div>
                  <div className="text-sm text-muted-foreground">Incorrect</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{getSessionTime()}</div>
                  <div className="text-sm text-muted-foreground">Time</div>
                </div>
              </div>
              <div className="flex gap-4 justify-center">
                <Button onClick={resetSession}>Study Again</Button>
                <Link href="/study/dashboard">
                  <Button variant="outline">Back to Dashboard</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Flashcard Study Session</h1>
            <p className="text-muted-foreground">Biology Chapter 5 - Cell Structure</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="gap-1">
              <Clock className="h-3 w-3" />
              {getSessionTime()}
            </Badge>
            <Badge variant="secondary">{currentCard?.topic}</Badge>
          </div>
        </div>

        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">
              Card {currentIndex + 1} of {filteredCards.length}
            </span>
            <div className="flex items-center gap-4 text-sm">
              <span className="text-green-600">✓ {correctCount}</span>
              <span className="text-red-600">✗ {incorrectCount}</span>
            </div>
          </div>
          <Progress value={progress} />
        </div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Controls Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3">Study Controls</h3>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full" onClick={shuffleCards}>
                    <Shuffle className="h-4 w-4 mr-2" />
                    Shuffle
                  </Button>
                  <Button variant="outline" size="sm" className="w-full" onClick={resetSession}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                  <Button variant="outline" size="sm" className="w-full">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3">Study Mode</h3>
                <div className="space-y-2">
                  <Button
                    variant={studyMode === "unmastered" ? "default" : "outline"}
                    size="sm"
                    className="w-full"
                    onClick={() => setStudyMode("unmastered")}
                  >
                    <Target className="h-4 w-4 mr-2" />
                    Unmastered
                  </Button>
                  <Button
                    variant={studyMode === "all" ? "default" : "outline"}
                    size="sm"
                    className="w-full"
                    onClick={() => setStudyMode("all")}
                  >
                    <Brain className="h-4 w-4 mr-2" />
                    All Cards
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3">Session Stats</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Accuracy</span>
                    <span>
                      {correctCount + incorrectCount > 0
                        ? Math.round((correctCount / (correctCount + incorrectCount)) * 100)
                        : 0}
                      %
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cards/min</span>
                    <span>
                      {Math.round(
                        ((correctCount + incorrectCount) / Math.max(1, (Date.now() - sessionStartTime) / 60000)) * 10,
                      ) / 10}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>XP Earned</span>
                    <span>{correctCount * 10}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Flashcard */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              <FlashcardComponent card={currentCard} isFlipped={isFlipped} onFlip={() => setIsFlipped(!isFlipped)} />

              {/* Navigation and Actions */}
              <div className="flex items-center justify-between">
                <Button variant="outline" onClick={handlePrevious} disabled={currentIndex === 0}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>

                {isFlipped && (
                  <div className="flex gap-3">
                    <Button variant="outline" onClick={handleIncorrect} className="text-red-600 border-red-200">
                      <XCircle className="h-4 w-4 mr-2" />
                      Incorrect
                    </Button>
                    <Button onClick={handleCorrect} className="bg-green-600 hover:bg-green-700">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Correct
                    </Button>
                  </div>
                )}

                <Button variant="outline" onClick={handleNext} disabled={currentIndex === filteredCards.length - 1}>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>

              {!isFlipped && (
                <div className="text-center">
                  <p className="text-muted-foreground mb-4">Click the card to reveal the answer</p>
                  <Button variant="ghost" onClick={() => setIsFlipped(true)}>
                    Reveal Answer
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
