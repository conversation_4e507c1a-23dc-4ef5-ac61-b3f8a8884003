#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update the demo user with a password
 */

const { MongoClient } = require('mongodb')
const bcrypt = require('bcryptjs')

const MONGODB_URI = 'mongodb://localhost:27017/learnify'

async function updateDemoUser() {
  console.log('🔄 Updating demo user with password...\n')
  
  let client
  
  try {
    // Connect to MongoDB
    console.log('1️⃣ Connecting to MongoDB...')
    client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
    })
    await client.connect()
    console.log('   ✅ Connected successfully!')
    
    // Get database
    const db = client.db('learnify')
    const usersCollection = db.collection('users')
    
    // Hash password
    console.log('\n2️⃣ Hashing password...')
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash('demo123', salt)
    console.log('   ✅ Password hashed successfully!')
    
    // Create or update demo user
    console.log('\n3️⃣ Creating/updating demo user...')
    const result = await usersCollection.replaceOne(
      { email: '<EMAIL>' },
      {
        email: '<EMAIL>',
        name: 'Demo User',
        password: hashedPassword,
        avatar: null,
        preferences: {
          theme: 'system',
          studyReminders: true,
          emailNotifications: true
        },
        stats: {
          totalStudyTime: 127,
          documentsUploaded: 1,
          quizzesCompleted: 3,
          flashcardsReviewed: 25
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      { upsert: true }
    )

    if (result.upsertedCount > 0) {
      console.log('   ✅ Demo user created successfully!')
    } else if (result.modifiedCount > 0) {
      console.log('   ✅ Demo user updated successfully!')
    }
    console.log('   📧 Email: <EMAIL>')
    console.log('   🔑 Password: demo123')
    
    console.log('\n🎉 Demo user update completed!')
    
  } catch (error) {
    console.error('\n❌ Update failed:', error.message)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
      console.log('\n🔌 Database connection closed')
    }
  }
}

// Run the update
if (require.main === module) {
  updateDemoUser()
    .then(() => {
      console.log('\n✅ Update completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error)
      process.exit(1)
    })
}

module.exports = { updateDemoUser }
