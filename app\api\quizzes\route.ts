import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Quiz } from '@/models'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const documentId = searchParams.get('documentId')
    const tags = searchParams.get('tags')?.split(',').filter(Boolean)
    const difficulty = searchParams.get('difficulty')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = parseInt(searchParams.get('skip') || '0')
    
    let query: any = { isActive: true }
    
    if (userId) {
      query.userId = userId
    }
    
    if (documentId) {
      query.documentId = documentId
    }
    
    if (difficulty) {
      query.difficulty = difficulty
    }
    
    if (tags && tags.length > 0) {
      query.tags = { $in: tags.map(tag => tag.toLowerCase()) }
    }
    
    const quizzes = await Quiz.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip)
    
    const total = await Quiz.countDocuments(query)
    
    return NextResponse.json({
      success: true,
      data: quizzes,
      pagination: {
        total,
        limit,
        skip,
        hasMore: skip + limit < total
      }
    })
    
  } catch (error) {
    console.error('Error fetching quizzes:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch quizzes' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const {
      userId,
      documentId,
      title,
      description,
      questions,
      tags,
      difficulty,
      timeLimit,
      settings
    } = body
    
    // Validate required fields
    if (!userId || !documentId || !title || !questions || !Array.isArray(questions) || questions.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields or invalid questions' },
        { status: 400 }
      )
    }
    
    // Validate questions
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i]
      if (!question.question || !question.options || !Array.isArray(question.options) || 
          question.options.length < 2 || typeof question.correctAnswer !== 'number' ||
          question.correctAnswer < 0 || question.correctAnswer >= question.options.length) {
        return NextResponse.json(
          { success: false, message: `Invalid question at index ${i}` },
          { status: 400 }
        )
      }
    }
    
    // Create new quiz
    const quizData = {
      userId,
      documentId,
      title,
      description: description || '',
      questions,
      tags: tags || [],
      difficulty: difficulty || 'medium',
      timeLimit: timeLimit || 300,
      settings: settings || {}
    }
    
    const quiz = new Quiz(quizData)
    await quiz.save()
    
    return NextResponse.json({
      success: true,
      message: 'Quiz created successfully',
      data: quiz
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error creating quiz:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to create quiz' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Quiz ID is required' },
        { status: 400 }
      )
    }
    
    const quiz = await Quiz.findById(id)
    if (!quiz) {
      return NextResponse.json(
        { success: false, message: 'Quiz not found' },
        { status: 404 }
      )
    }
    
    // Update quiz fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        quiz[key] = updateData[key]
      }
    })
    
    await quiz.save()
    
    return NextResponse.json({
      success: true,
      message: 'Quiz updated successfully',
      data: quiz
    })
    
  } catch (error) {
    console.error('Error updating quiz:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to update quiz' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Quiz ID is required' },
        { status: 400 }
      )
    }
    
    const quiz = await Quiz.findById(id)
    if (!quiz) {
      return NextResponse.json(
        { success: false, message: 'Quiz not found' },
        { status: 404 }
      )
    }
    
    // Soft delete by setting isActive to false
    quiz.isActive = false
    await quiz.save()
    
    return NextResponse.json({
      success: true,
      message: 'Quiz deleted successfully'
    })
    
  } catch (error) {
    console.error('Error deleting quiz:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to delete quiz' },
      { status: 500 }
    )
  }
}
