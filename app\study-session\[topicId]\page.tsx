"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er, useSearchParams } from "next/navigation"
import { Navigation } from "@/components/navigation"
import { ProtectedRoute } from "@/components/protected-route"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  ArrowLeft, 
  Brain, 
  Target, 
  CheckCircle,
  RotateCcw,
  Eye,
  EyeOff,
  ChevronLeft,
  ChevronRight,
  Clock,
  BookOpen,
  Award,
  Lightbulb
} from "lucide-react"
import Link from "next/link"

interface StudyTopic {
  id: string
  title: string
  description: string
  keyPoints: string[]
  summary: string
  motivationTip: string
  estimatedDuration: number
  difficulty: string
}

interface Flashcard {
  id: string
  front: string
  back: string
  difficulty: string
}

interface QuizQuestion {
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
  difficulty: string
}

interface Quiz {
  id: string
  title: string
  description: string
  questions: QuizQuestion[]
  timeLimit: number
  difficulty: string
}

interface StudyContent {
  topic: StudyTopic
  flashcards: Flashcard[]
  quiz: Quiz
}

export default function StudySessionPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useAuth()
  
  const [studyContent, setStudyContent] = useState<StudyContent | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedTab, setSelectedTab] = useState("overview")
  
  // Flashcard state
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [showAnswer, setShowAnswer] = useState(false)
  const [reviewedCards, setReviewedCards] = useState<Set<number>>(new Set())
  
  // Quiz state
  const [quizStarted, setQuizStarted] = useState(false)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: number]: number }>({})
  const [quizCompleted, setQuizCompleted] = useState(false)
  const [quizScore, setQuizScore] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(0)
  
  // Session tracking
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
  const [sessionCompleted, setSessionCompleted] = useState(false)

  const planId = searchParams.get('planId')

  useEffect(() => {
    if (user && params.topicId && planId) {
      fetchStudyContent()
      setSessionStartTime(new Date())
    }
  }, [user, params.topicId, planId])

  useEffect(() => {
    if (quizStarted && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (quizStarted && timeRemaining === 0) {
      completeQuiz()
    }
  }, [quizStarted, timeRemaining])

  const fetchStudyContent = async () => {
    try {
      const response = await fetch('/api/study-plans/generate-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studyPlanId: planId,
          topicId: params.topicId,
          userId: user?.id
        }),
      })

      if (response.ok) {
        const data = await response.json()
        setStudyContent(data.data)
        setTimeRemaining(data.data.quiz.timeLimit)
      }
    } catch (error) {
      console.error('Failed to fetch study content:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const nextCard = () => {
    if (studyContent && currentCardIndex < studyContent.flashcards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1)
      setShowAnswer(false)
    }
  }

  const prevCard = () => {
    if (currentCardIndex > 0) {
      setCurrentCardIndex(currentCardIndex - 1)
      setShowAnswer(false)
    }
  }

  const markCardReviewed = (isCorrect: boolean) => {
    setReviewedCards(prev => new Set([...prev, currentCardIndex]))
    // Here you could also send the review result to the API
    nextCard()
  }

  const startQuiz = () => {
    setQuizStarted(true)
    setCurrentQuestionIndex(0)
    setSelectedAnswers({})
    setQuizCompleted(false)
    if (studyContent) {
      setTimeRemaining(studyContent.quiz.timeLimit)
    }
  }

  const selectAnswer = (questionIndex: number, answerIndex: number) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionIndex]: answerIndex
    }))
  }

  const nextQuestion = () => {
    if (studyContent && currentQuestionIndex < studyContent.quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    } else {
      completeQuiz()
    }
  }

  const completeQuiz = () => {
    if (!studyContent) return
    
    let correct = 0
    studyContent.quiz.questions.forEach((question, index) => {
      if (selectedAnswers[index] === question.correctAnswer) {
        correct++
      }
    })
    
    const score = Math.round((correct / studyContent.quiz.questions.length) * 100)
    setQuizScore(score)
    setQuizCompleted(true)
    setQuizStarted(false)
  }

  const completeSession = async () => {
    if (!sessionStartTime || !studyContent) return
    
    const sessionEndTime = new Date()
    const actualDuration = Math.round((sessionEndTime.getTime() - sessionStartTime.getTime()) / (1000 * 60))
    
    try {
      // Mark topic as completed
      await fetch('/api/study-plans', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studyPlanId: planId,
          userId: user?.id,
          action: 'complete-topic',
          data: { 
            topicId: params.topicId, 
            actualDuration 
          }
        }),
      })
      
      setSessionCompleted(true)
    } catch (error) {
      console.error('Failed to complete session:', error)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navigation />
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (!studyContent) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navigation />
          <div className="container mx-auto px-4 py-8">
            <Card>
              <CardContent className="text-center py-12">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Study content not found</h3>
                <p className="text-muted-foreground mb-4">
                  Unable to load the study content for this topic.
                </p>
                <Link href={`/study-plan/${planId}`}>
                  <Button>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Study Plan
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (sessionCompleted) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navigation />
          <div className="container mx-auto px-4 py-8">
            <Card className="max-w-2xl mx-auto">
              <CardContent className="text-center py-12">
                <Award className="h-16 w-16 text-yellow-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">Session Completed! 🎉</h2>
                <p className="text-muted-foreground mb-6">
                  Great job completing the study session for "{studyContent.topic.title}"
                </p>
                
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-600 font-medium">Flashcards Reviewed</p>
                    <p className="text-2xl font-bold text-blue-700">{reviewedCards.size}/{studyContent.flashcards.length}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-green-600 font-medium">Quiz Score</p>
                    <p className="text-2xl font-bold text-green-700">{quizScore}%</p>
                  </div>
                </div>
                
                <div className="flex gap-4 justify-center">
                  <Link href={`/study-plan/${planId}`}>
                    <Button>
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Study Plan
                    </Button>
                  </Link>
                  <Link href="/study/dashboard">
                    <Button variant="outline">
                      Go to Dashboard
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center gap-4 mb-6">
            <Link href={`/study-plan/${planId}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-2xl font-bold">{studyContent.topic.title}</h1>
              <p className="text-muted-foreground">{studyContent.topic.description}</p>
            </div>
            <Badge className={`${
              studyContent.topic.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
              studyContent.topic.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {studyContent.topic.difficulty}
            </Badge>
          </div>

          {/* Progress */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Session Progress</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(((reviewedCards.size / studyContent.flashcards.length) + (quizCompleted ? 1 : 0)) / 2 * 100)}%
                </span>
              </div>
              <Progress 
                value={((reviewedCards.size / studyContent.flashcards.length) + (quizCompleted ? 1 : 0)) / 2 * 100} 
                className="mb-4" 
              />
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-sm text-muted-foreground">Flashcards</p>
                  <p className="font-semibold">{reviewedCards.size}/{studyContent.flashcards.length}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Quiz</p>
                  <p className="font-semibold">{quizCompleted ? 'Completed' : 'Pending'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Time</p>
                  <p className="font-semibold">{studyContent.topic.estimatedDuration} min</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Main Content */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="flashcards">Flashcards</TabsTrigger>
              <TabsTrigger value="quiz">Quiz</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Topic Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Key Learning Points:</h4>
                    <ul className="space-y-2">
                      {studyContent.topic.keyPoints.map((point, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-800 mb-2">Summary:</h4>
                    <p className="text-blue-700 text-sm">{studyContent.topic.summary}</p>
                  </div>
                  
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <div className="flex items-start gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-yellow-800 mb-1">Study Tip:</h4>
                        <p className="text-yellow-700 text-sm">{studyContent.topic.motivationTip}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="flashcards" className="space-y-6">
              {studyContent.flashcards.length > 0 ? (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Brain className="h-5 w-5" />
                        Flashcards ({currentCardIndex + 1}/{studyContent.flashcards.length})
                      </CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAnswer(!showAnswer)}
                      >
                        {showAnswer ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        {showAnswer ? 'Hide' : 'Show'} Answer
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="min-h-[200px] flex items-center justify-center">
                      <div className="text-center">
                        <div className="bg-gray-50 p-6 rounded-lg mb-4">
                          <h3 className="text-lg font-semibold mb-2">Question:</h3>
                          <p>{studyContent.flashcards[currentCardIndex].front}</p>
                        </div>
                        
                        {showAnswer && (
                          <div className="bg-blue-50 p-6 rounded-lg">
                            <h3 className="text-lg font-semibold mb-2 text-blue-800">Answer:</h3>
                            <p className="text-blue-700">{studyContent.flashcards[currentCardIndex].back}</p>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Button
                        variant="outline"
                        onClick={prevCard}
                        disabled={currentCardIndex === 0}
                      >
                        <ChevronLeft className="h-4 w-4 mr-2" />
                        Previous
                      </Button>
                      
                      {showAnswer && (
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            onClick={() => markCardReviewed(false)}
                            className="text-red-600 border-red-200"
                          >
                            Need Review
                          </Button>
                          <Button
                            onClick={() => markCardReviewed(true)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Got It!
                          </Button>
                        </div>
                      )}
                      
                      <Button
                        variant="outline"
                        onClick={nextCard}
                        disabled={currentCardIndex === studyContent.flashcards.length - 1}
                      >
                        Next
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    </div>
                    
                    <Progress 
                      value={(reviewedCards.size / studyContent.flashcards.length) * 100} 
                      className="mt-4"
                    />
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="text-center py-12">
                    <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No flashcards available</h3>
                    <p className="text-muted-foreground">
                      Flashcards for this topic are being generated.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="quiz" className="space-y-6">
              {!quizStarted && !quizCompleted ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      {studyContent.quiz.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground">{studyContent.quiz.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg text-center">
                        <p className="text-sm text-muted-foreground">Questions</p>
                        <p className="text-2xl font-bold">{studyContent.quiz.questions.length}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg text-center">
                        <p className="text-sm text-muted-foreground">Time Limit</p>
                        <p className="text-2xl font-bold">{formatTime(studyContent.quiz.timeLimit)}</p>
                      </div>
                    </div>
                    
                    <Button onClick={startQuiz} className="w-full">
                      <Target className="h-4 w-4 mr-2" />
                      Start Quiz
                    </Button>
                  </CardContent>
                </Card>
              ) : quizStarted ? (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>
                        Question {currentQuestionIndex + 1} of {studyContent.quiz.questions.length}
                      </CardTitle>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="h-4 w-4" />
                        {formatTime(timeRemaining)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">
                        {studyContent.quiz.questions[currentQuestionIndex].question}
                      </h3>
                      
                      <div className="space-y-3">
                        {studyContent.quiz.questions[currentQuestionIndex].options.map((option, index) => (
                          <button
                            key={index}
                            onClick={() => selectAnswer(currentQuestionIndex, index)}
                            className={`w-full p-4 text-left border rounded-lg transition-colors ${
                              selectedAnswers[currentQuestionIndex] === index
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <span className="font-medium mr-3">{String.fromCharCode(65 + index)}.</span>
                            {option}
                          </button>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex justify-between">
                      <Button
                        variant="outline"
                        onClick={() => setCurrentQuestionIndex(Math.max(0, currentQuestionIndex - 1))}
                        disabled={currentQuestionIndex === 0}
                      >
                        Previous
                      </Button>
                      
                      <Button
                        onClick={nextQuestion}
                        disabled={selectedAnswers[currentQuestionIndex] === undefined}
                      >
                        {currentQuestionIndex === studyContent.quiz.questions.length - 1 ? 'Finish' : 'Next'}
                      </Button>
                    </div>
                    
                    <Progress 
                      value={((currentQuestionIndex + 1) / studyContent.quiz.questions.length) * 100} 
                    />
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Award className="h-5 w-5" />
                      Quiz Results
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="text-center">
                      <div className="text-4xl font-bold mb-2">{quizScore}%</div>
                      <p className="text-muted-foreground">
                        You got {Object.values(selectedAnswers).filter((answer, index) => 
                          answer === studyContent.quiz.questions[index].correctAnswer
                        ).length} out of {studyContent.quiz.questions.length} questions correct
                      </p>
                    </div>
                    
                    <div className="space-y-4">
                      {studyContent.quiz.questions.map((question, index) => {
                        const userAnswer = selectedAnswers[index]
                        const isCorrect = userAnswer === question.correctAnswer
                        
                        return (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="flex items-start gap-2 mb-2">
                              {isCorrect ? (
                                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                              ) : (
                                <div className="w-5 h-5 border-2 border-red-500 rounded-full mt-0.5" />
                              )}
                              <div className="flex-1">
                                <p className="font-medium">{question.question}</p>
                                <p className="text-sm text-muted-foreground mt-1">
                                  Your answer: {question.options[userAnswer]} 
                                  {!isCorrect && (
                                    <span className="text-green-600 ml-2">
                                      (Correct: {question.options[question.correctAnswer]})
                                    </span>
                                  )}
                                </p>
                                {question.explanation && (
                                  <p className="text-sm text-blue-600 mt-2">{question.explanation}</p>
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                    
                    <Button onClick={startQuiz} variant="outline" className="w-full">
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Retake Quiz
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="summary" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Session Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg text-center">
                      <p className="text-sm text-blue-600 font-medium">Flashcards Reviewed</p>
                      <p className="text-2xl font-bold text-blue-700">{reviewedCards.size}/{studyContent.flashcards.length}</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg text-center">
                      <p className="text-sm text-green-600 font-medium">Quiz Score</p>
                      <p className="text-2xl font-bold text-green-700">{quizCompleted ? `${quizScore}%` : 'Not taken'}</p>
                    </div>
                  </div>
                  
                  {reviewedCards.size === studyContent.flashcards.length && quizCompleted && (
                    <div className="text-center">
                      <Button onClick={completeSession} size="lg" className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-5 w-5 mr-2" />
                        Complete Study Session
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </ProtectedRoute>
  )
}
