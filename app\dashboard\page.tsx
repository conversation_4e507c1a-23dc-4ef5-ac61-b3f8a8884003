"use client"

import { Navigation } from "@/components/navigation"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, Trophy, Clock, BookOpen, Brain, Target, TrendingUp, Calendar } from "lucide-react"

const recentActivity = [
  { id: 1, type: "3D Lab", item: "Heart Anatomy", time: "2 hours ago", system: "Circulatory" },
  { id: 2, type: "Quiz", item: "Skeletal System Quiz", score: "85%", time: "1 day ago", system: "Skeletal" },
  { id: 3, type: "AI Chat", item: "Asked about lung function", time: "2 days ago", system: "Respiratory" },
  { id: 4, type: "AR View", item: "Brain Structure", time: "3 days ago", system: "Nervous" },
]

const systemProgress = [
  { name: "Circulatory System", progress: 85, color: "bg-red-500" },
  { name: "Skeletal System", progress: 72, color: "bg-blue-500" },
  { name: "Nervous System", progress: 60, color: "bg-purple-500" },
  { name: "Respiratory System", progress: 45, color: "bg-green-500" },
  { name: "Digestive System", progress: 30, color: "bg-orange-500" },
]

const achievements = [
  { name: "First Quiz", description: "Completed your first quiz", earned: true },
  { name: "3D Explorer", description: "Explored 10 different organs in 3D", earned: true },
  { name: "AI Curious", description: "Asked 25 questions to Bolt AI", earned: true },
  { name: "Quiz Master", description: "Scored 90% or higher on 5 quizzes", earned: false },
  { name: "AR Pioneer", description: "Used AR viewer 10 times", earned: false },
]

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Learning Dashboard</h1>
          <p className="text-muted-foreground">Track your anatomy learning progress</p>
        </div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profile
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <Avatar className="w-20 h-20 mx-auto mb-4">
                  <AvatarImage src="/placeholder.svg?height=80&width=80" />
                  <AvatarFallback>JD</AvatarFallback>
                </Avatar>
                <h3 className="font-semibold mb-1">John Doe</h3>
                <p className="text-sm text-muted-foreground mb-4">Medical Student</p>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Study Streak</span>
                    <Badge variant="secondary">7 days</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Points</span>
                    <Badge>2,450</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Rank</span>
                    <Badge variant="outline">#23</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Weekly Goal
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Study Time</span>
                    <span>12h / 15h</span>
                  </div>
                  <Progress value={80} />
                  <p className="text-xs text-muted-foreground">3 hours left to reach your goal!</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Stats Cards */}
            <div className="grid md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-2xl font-bold">24h</p>
                      <p className="text-xs text-muted-foreground">Study Time</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Trophy className="h-5 w-5 text-yellow-500" />
                    <div>
                      <p className="text-2xl font-bold">12</p>
                      <p className="text-xs text-muted-foreground">Quizzes Completed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-purple-500" />
                    <div>
                      <p className="text-2xl font-bold">87%</p>
                      <p className="text-xs text-muted-foreground">Avg Quiz Score</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-2xl font-bold">45</p>
                      <p className="text-xs text-muted-foreground">Topics Studied</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* System Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  System Mastery Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {systemProgress.map((system) => (
                    <div key={system.name}>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">{system.name}</span>
                        <span className="text-sm text-muted-foreground">{system.progress}%</span>
                      </div>
                      <Progress value={system.progress} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-2 gap-6">
              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent">
                        <div className="w-2 h-2 bg-primary rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">{activity.item}</p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Badge variant="outline" className="text-xs">
                              {activity.type}
                            </Badge>
                            <span>{activity.time}</span>
                            {activity.score && <span>• {activity.score}</span>}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button variant="outline" className="w-full mt-4">
                    View All Activity
                  </Button>
                </CardContent>
              </Card>

              {/* Achievements */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Trophy className="h-5 w-5" />
                    Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {achievements.map((achievement, index) => (
                      <div
                        key={index}
                        className={`flex items-center gap-3 p-2 rounded-lg ${
                          achievement.earned ? "bg-accent" : "opacity-50"
                        }`}
                      >
                        <Trophy
                          className={`h-4 w-4 ${achievement.earned ? "text-yellow-500" : "text-muted-foreground"}`}
                        />
                        <div className="flex-1">
                          <p className="text-sm font-medium">{achievement.name}</p>
                          <p className="text-xs text-muted-foreground">{achievement.description}</p>
                        </div>
                        {achievement.earned && (
                          <Badge variant="secondary" className="text-xs">
                            Earned
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
