"use client"

import { useState, useEffect } from "react"
import { Navigation } from "@/components/navigation"
import { ProtectedRoute } from "@/components/protected-route"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  FileText, 
  Calendar, 
  Clock, 
  BookOpen, 
  Brain, 
  Target, 
  Search,
  Filter,
  Plus,
  Eye,
  Play,
  MoreVertical
} from "lucide-react"
import Link from "next/link"

interface Document {
  id: string
  title: string
  filename: string
  subject: string
  difficulty: string
  fileSize: number
  pageCount: number
  wordCount: number
  hasStudyPlan: boolean
  studyPlanId?: string
  createdAt: string
  analysisData?: {
    mainTopics: string[]
    complexity: number
    estimatedStudyTime: number
  }
}

interface StudyPlan {
  id: string
  title: string
  completionPercentage: number
  totalTopics: number
  completedTopics: number
  daysRemaining: number
  todaysSchedule: any[]
}

export default function DocumentsPage() {
  const { user } = useAuth()
  const [documents, setDocuments] = useState<Document[]>([])
  const [studyPlans, setStudyPlans] = useState<{ [key: string]: StudyPlan }>({})
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterSubject, setFilterSubject] = useState("all")
  const [filterDifficulty, setFilterDifficulty] = useState("all")

  useEffect(() => {
    if (user) {
      fetchDocuments()
    }
  }, [user])

  const fetchDocuments = async () => {
    try {
      const response = await fetch(`/api/documents?userId=${user?.id}`)
      if (response.ok) {
        const data = await response.json()
        setDocuments(data.data)
        
        // Fetch study plans for documents that have them
        const docsWithPlans = data.data.filter((doc: Document) => doc.hasStudyPlan)
        if (docsWithPlans.length > 0) {
          fetchStudyPlans(docsWithPlans)
        }
      }
    } catch (error) {
      console.error('Failed to fetch documents:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchStudyPlans = async (documents: Document[]) => {
    try {
      const response = await fetch(`/api/study-plans?userId=${user?.id}&active=true`)
      if (response.ok) {
        const data = await response.json()
        const plansMap: { [key: string]: StudyPlan } = {}
        data.data.forEach((plan: any) => {
          plansMap[plan.documentId] = plan
        })
        setStudyPlans(plansMap)
      }
    } catch (error) {
      console.error('Failed to fetch study plans:', error)
    }
  }

  const generateStudyPlan = async (documentId: string) => {
    try {
      const response = await fetch('/api/study-plans/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId,
          userId: user?.id,
          preferences: {
            dailyStudyTime: 60,
            preferredDays: [1, 2, 3, 4, 5],
            bufferDays: true,
            revisionSlots: true
          }
        }),
      })

      if (response.ok) {
        fetchDocuments() // Refresh documents
      }
    } catch (error) {
      console.error('Failed to generate study plan:', error)
    }
  }

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.subject.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSubject = filterSubject === "all" || doc.subject === filterSubject
    const matchesDifficulty = filterDifficulty === "all" || doc.difficulty === filterDifficulty
    
    return matchesSearch && matchesSubject && matchesDifficulty
  })

  const subjects = [...new Set(documents.map(doc => doc.subject))]

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold mb-2">My Documents</h1>
              <p className="text-muted-foreground">Manage your study materials and learning plans</p>
            </div>
            <Link href="/upload">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Upload PDF
              </Button>
            </Link>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search documents..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={filterSubject} onValueChange={setFilterSubject}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Filter by subject" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Subjects</SelectItem>
                    {subjects.map(subject => (
                      <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={filterDifficulty} onValueChange={setFilterDifficulty}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Filter by difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Difficulties</SelectItem>
                    <SelectItem value="easy">Easy</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="hard">Hard</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Documents Grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded mb-4"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredDocuments.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No documents found</h3>
                <p className="text-muted-foreground mb-4">
                  {documents.length === 0 
                    ? "Upload your first PDF to get started with AI-powered study plans"
                    : "Try adjusting your search or filter criteria"
                  }
                </p>
                {documents.length === 0 && (
                  <Link href="/upload">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Upload Your First PDF
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDocuments.map((document) => {
                const studyPlan = studyPlans[document.id]
                
                return (
                  <Card key={document.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg line-clamp-2 mb-2">
                            {document.title}
                          </CardTitle>
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="secondary">{document.subject}</Badge>
                            <Badge className={getDifficultyColor(document.difficulty)}>
                              {document.difficulty}
                            </Badge>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      {/* Document Info */}
                      <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <FileText className="h-4 w-4" />
                          <span>{document.pageCount} pages</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{formatFileSize(document.fileSize)}</span>
                        </div>
                      </div>

                      {/* Study Plan Status */}
                      {document.hasStudyPlan && studyPlan ? (
                        <div className="space-y-3">
                          <div className="flex items-center justify-between text-sm">
                            <span className="font-medium">Study Progress</span>
                            <span>{studyPlan.completionPercentage}%</span>
                          </div>
                          <Progress value={studyPlan.completionPercentage} className="h-2" />
                          <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                            <div>{studyPlan.completedTopics}/{studyPlan.totalTopics} topics</div>
                            <div>{studyPlan.daysRemaining} days left</div>
                          </div>
                          
                          {studyPlan.todaysSchedule.length > 0 && (
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <div className="flex items-center gap-2 text-blue-700 text-sm font-medium mb-1">
                                <Calendar className="h-4 w-4" />
                                Today's Study
                              </div>
                              <p className="text-blue-600 text-sm">
                                {studyPlan.todaysSchedule.length} topic(s) scheduled
                              </p>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="bg-gray-50 p-3 rounded-lg text-center">
                          <BookOpen className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground mb-3">
                            No study plan yet
                          </p>
                          <Button 
                            size="sm" 
                            onClick={() => generateStudyPlan(document.id)}
                            className="w-full"
                          >
                            <Brain className="h-4 w-4 mr-2" />
                            Generate Study Plan
                          </Button>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        {document.hasStudyPlan && (
                          <Link href={`/study-plan/${document.studyPlanId}`} className="flex-1">
                            <Button size="sm" className="w-full">
                              <Play className="h-4 w-4 mr-2" />
                              Study
                            </Button>
                          </Link>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  )
}
