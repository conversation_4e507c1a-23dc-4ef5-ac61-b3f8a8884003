import { NextResponse } from 'next/server'
import { MongoClient } from 'mongodb'

export async function GET() {
  // Use hardcoded URI to ensure connection works
  const MONGODB_URI = 'mongodb://localhost:27017/learnify'
  
  try {
    console.log('🔄 Testing Learnify database connection...')
    console.log('📍 URI:', MONGODB_URI)
    
    const client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
    })
    
    await client.connect()
    console.log('✅ Connected to MongoDB successfully')
    
    const db = client.db('learnify')
    const collections = await db.listCollections().toArray()
    
    // Get collection counts
    const stats = {}
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments()
      stats[collection.name] = count
    }
    
    await client.close()
    console.log('🔌 Connection closed')
    
    return NextResponse.json({
      success: true,
      message: 'Learnify database connection successful',
      database: {
        name: 'learnify',
        uri: 'mongodb://localhost:27017/learnify',
        collections: collections.map(c => c.name),
        stats: stats
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    
    let errorMessage = error instanceof Error ? error.message : 'Unknown error'
    let troubleshooting = []
    
    if (errorMessage.includes('ECONNREFUSED')) {
      troubleshooting = [
        'Make sure MongoDB is running on localhost:27017',
        'Start MongoDB service:',
        '  Windows: net start MongoDB',
        '  macOS: brew services start mongodb-community',
        '  Linux: sudo systemctl start mongod'
      ]
    }
    
    return NextResponse.json({
      success: false,
      message: 'Database connection failed',
      error: errorMessage,
      troubleshooting: troubleshooting,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
