"use client"

import { useState } from "react"
import { <PERSON> } from "@/components/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  Brain,
  Trophy,
  Target,
  Clock,
  FileText,
  Users,
  TrendingUp,
  Play,
  RotateCcw,
  Star,
  Calendar,
  Upload,
} from "lucide-react"
import Link from "next/link"
import { useGame } from "@/components/game-provider"

const studySessions = [
  {
    id: 1,
    title: "Biology Chapter 5 - Cell Structure",
    subject: "Biology",
    progress: 75,
    flashcards: 24,
    quizzes: 3,
    lastStudied: "2 hours ago",
    difficulty: "Medium",
  },
  {
    id: 2,
    title: "Physics - Quantum Mechanics",
    subject: "Physics",
    progress: 45,
    flashcards: 18,
    quizzes: 2,
    lastStudied: "1 day ago",
    difficulty: "Hard",
  },
  {
    id: 3,
    title: "History - World War II",
    subject: "History",
    progress: 90,
    flashcards: 32,
    quizzes: 4,
    lastStudied: "3 hours ago",
    difficulty: "Easy",
  },
]

const recentActivity = [
  { action: "Completed flashcard set", subject: "Biology", time: "2 hours ago", xp: 50 },
  { action: "Scored 85% on quiz", subject: "Physics", time: "1 day ago", xp: 75 },
  { action: "Unlocked achievement", subject: "General", time: "2 days ago", xp: 100 },
  { action: "Study streak: 7 days", subject: "General", time: "3 days ago", xp: 25 },
]

export default function StudyDashboard() {
  const { userStats } = useGame()
  const [selectedTab, setSelectedTab] = useState("overview")

  const studyStats = [
    { label: "Total Study Time", value: "127h 32m", icon: Clock, color: "text-pink-400" },
    { label: "Materials Uploaded", value: "23", icon: Upload, color: "text-purple-400" },
    { label: "Flashcards Reviewed", value: "1,247", icon: Brain, color: "text-green-300" },
    { label: "Quizzes Completed", value: "45", icon: Target, color: "text-yellow-300" },
    { label: "Average Score", value: "87%", icon: Trophy, color: "text-rose-400" },
    { label: "Current Streak", value: `${userStats.streak} days`, icon: Calendar, color: "text-indigo-300" },
  ]

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Study Dashboard</h1>
          <p className="text-muted-foreground">Track your progress and continue your learning journey</p>
        </div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Stats Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            {studyStats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Icon className={`h-5 w-5 ${stat.color}`} />
                      </div>
                      <div>
                        <p className="text-2xl font-bold">{stat.value}</p>
                        <p className="text-sm text-muted-foreground">{stat.label}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/upload">
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <FileText className="h-4 w-4 mr-2" />
                    Upload New PDF
                  </Button>
                </Link>
                <Link href="/study/flashcards">
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Brain className="h-4 w-4 mr-2" />
                    Practice Flashcards
                  </Button>
                </Link>
                <Link href="/study/quiz">
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Target className="h-4 w-4 mr-2" />
                    Take Quiz
                  </Button>
                </Link>
                <Link href="/chat">
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Users className="h-4 w-4 mr-2" />
                    Ask AI Tutor
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="sessions">Study Sessions</TabsTrigger>
                <TabsTrigger value="progress">Progress</TabsTrigger>
                <TabsTrigger value="achievements">Achievements</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                {/* Daily Goals */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      Daily Goals
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Study Time</span>
                          <span>2h 15m / 3h</span>
                        </div>
                        <Progress value={75} />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Flashcards</span>
                          <span>47 / 50</span>
                        </div>
                        <Progress value={94} />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Quiz Score</span>
                          <span>85% / 80%</span>
                        </div>
                        <Progress value={100} />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Activity */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Recent Activity
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {recentActivity.map((activity, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div>
                            <p className="font-medium text-sm">{activity.action}</p>
                            <p className="text-xs text-muted-foreground">
                              {activity.subject} • {activity.time}
                            </p>
                          </div>
                          <Badge variant="secondary">+{activity.xp} XP</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="sessions" className="space-y-4">
                {studySessions.map((session) => (
                  <Card key={session.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="font-semibold mb-1">{session.title}</h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Badge variant="outline">{session.subject}</Badge>
                            <Badge variant="secondary">{session.difficulty}</Badge>
                            <span>Last studied {session.lastStudied}</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-primary">{session.progress}%</div>
                          <div className="text-xs text-muted-foreground">Complete</div>
                        </div>
                      </div>

                      <Progress value={session.progress} className="mb-4" />

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm">
                          <span>{session.flashcards} flashcards</span>
                          <span>{session.quizzes} quizzes</span>
                        </div>
                        <div className="flex gap-2">
                          <Link href="/study/flashcards">
                            <Button size="sm" variant="outline">
                              <Play className="h-4 w-4 mr-1" />
                              Continue
                            </Button>
                          </Link>
                          <Button size="sm" variant="ghost">
                            <RotateCcw className="h-4 w-4 mr-1" />
                            Review
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="progress" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Weekly Progress
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Study Hours</span>
                          <span className="font-bold">18.5h</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Cards Reviewed</span>
                          <span className="font-bold">324</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Quizzes Taken</span>
                          <span className="font-bold">12</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Average Score</span>
                          <span className="font-bold">87%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Subject Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Biology</span>
                            <span>45%</span>
                          </div>
                          <Progress value={45} />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Physics</span>
                            <span>30%</span>
                          </div>
                          <Progress value={30} />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>History</span>
                            <span>25%</span>
                          </div>
                          <Progress value={25} />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="achievements" className="space-y-4">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { name: "First Steps", description: "Complete your first study session", earned: true },
                    { name: "Flashcard Master", description: "Review 100 flashcards", earned: true },
                    { name: "Quiz Hero", description: "Score 90% or higher on 5 quizzes", earned: true },
                    { name: "Week Warrior", description: "Study for 7 consecutive days", earned: false },
                    { name: "Knowledge Seeker", description: "Upload 10 different PDFs", earned: false },
                    { name: "Perfect Score", description: "Get 100% on any quiz", earned: false },
                  ].map((achievement, index) => (
                    <Card key={index} className={achievement.earned ? "border-yellow-200 bg-yellow-50" : ""}>
                      <CardContent className="p-4 text-center">
                        <div
                          className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${
                            achievement.earned ? "bg-yellow-100 text-yellow-600" : "bg-muted text-muted-foreground"
                          }`}
                        >
                          <Star className="h-6 w-6" />
                        </div>
                        <h3 className="font-semibold mb-1">{achievement.name}</h3>
                        <p className="text-xs text-muted-foreground">{achievement.description}</p>
                        {achievement.earned && (
                          <Badge variant="secondary" className="mt-2">
                            Earned
                          </Badge>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
