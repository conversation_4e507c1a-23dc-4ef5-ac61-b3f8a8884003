"use client"

import { useRef, useState } from "react"
import { useFrame } from "@react-three/fiber"
import { Html, Text } from "@react-three/drei"
import type * as THREE from "three"

interface AnatomyModelProps {
  selectedSystem: string
  onOrganClick: (organ: string | null) => void
  showLabels: boolean
}

export function AnatomyModel({ selectedSystem, onOrganClick, showLabels }: AnatomyModelProps) {
  const groupRef = useRef<THREE.Group>(null)
  const [hoveredOrgan, setHoveredOrgan] = useState<string | null>(null)

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.1
    }
  })

  const organs = [
    { name: "<PERSON>", position: [0, 0.5, 0], color: "#ff6b6b", system: "circulatory" },
    { name: "Lung<PERSON>", position: [-0.8, 0.3, 0], color: "#4ecdc4", system: "respiratory" },
    { name: "Liver", position: [0.6, -0.2, 0], color: "#45b7d1", system: "digestive" },
    { name: "<PERSON>", position: [0, 1.5, 0], color: "#96ceb4", system: "nervous" },
    { name: "Kidneys", position: [-0.4, -0.8, 0], color: "#ffeaa7", system: "urinary" },
  ]

  const getOrganOpacity = (organSystem: string) => {
    if (selectedSystem === "all") return 1
    return selectedSystem === organSystem ? 1 : 0.3
  }

  return (
    <group ref={groupRef}>
      {organs.map((organ) => (
        <group key={organ.name} position={organ.position}>
          <mesh
            onClick={() => onOrganClick(organ.name)}
            onPointerOver={() => setHoveredOrgan(organ.name)}
            onPointerOut={() => setHoveredOrgan(null)}
          >
            <sphereGeometry args={[0.3, 32, 32]} />
            <meshStandardMaterial
              color={organ.color}
              transparent
              opacity={getOrganOpacity(organ.system)}
              emissive={hoveredOrgan === organ.name ? organ.color : "#000000"}
              emissiveIntensity={hoveredOrgan === organ.name ? 0.2 : 0}
            />
          </mesh>

          {showLabels && (
            <Html distanceFactor={10} position={[0, 0.5, 0]}>
              <div className="bg-black/80 text-white px-2 py-1 rounded text-xs whitespace-nowrap">{organ.name}</div>
            </Html>
          )}

          {hoveredOrgan === organ.name && (
            <Text position={[0, -0.6, 0]} fontSize={0.1} color="white" anchorX="center" anchorY="middle">
              Click to learn more
            </Text>
          )}
        </group>
      ))}

      {/* Body outline */}
      <mesh position={[0, 0, -0.5]}>
        <cylinderGeometry args={[0.8, 1, 3, 8]} />
        <meshStandardMaterial color="#e0e0e0" transparent opacity={0.1} wireframe />
      </mesh>
    </group>
  )
}
