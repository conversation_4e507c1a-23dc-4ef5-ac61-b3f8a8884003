import mongoose, { Document, Schema } from 'mongoose'

export interface I<PERSON><PERSON> extends Document {
  email: string
  name: string
  password: string
  avatar?: string
  preferences: {
    theme: 'light' | 'dark' | 'system'
    studyReminders: boolean
    emailNotifications: boolean
  }
  stats: {
    totalStudyTime: number
    documentsUploaded: number
    quizzesCompleted: number
    flashcardsReviewed: number
  }
  createdAt: Date
  updatedAt: Date
}

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  avatar: {
    type: String,
    default: null
  },
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'system'
    },
    studyReminders: {
      type: Boolean,
      default: true
    },
    emailNotifications: {
      type: Boolean,
      default: true
    }
  },
  stats: {
    totalStudyTime: {
      type: Number,
      default: 0,
      min: [0, 'Study time cannot be negative']
    },
    documentsUploaded: {
      type: Number,
      default: 0,
      min: [0, 'Documents uploaded cannot be negative']
    },
    quizzesCompleted: {
      type: Number,
      default: 0,
      min: [0, 'Quizzes completed cannot be negative']
    },
    flashcardsReviewed: {
      type: Number,
      default: 0,
      min: [0, 'Flashcards reviewed cannot be negative']
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
UserSchema.index({ email: 1 }, { unique: true })
UserSchema.index({ createdAt: -1 })

// Virtual for user's full profile
UserSchema.virtual('profile').get(function() {
  return {
    id: this._id,
    email: this.email,
    name: this.name,
    avatar: this.avatar,
    preferences: this.preferences,
    stats: this.stats,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt
  }
})

// Methods
UserSchema.methods.updateStats = function(statType: keyof IUser['stats'], increment: number = 1) {
  this.stats[statType] += increment
  return this.save()
}

UserSchema.methods.updatePreferences = function(newPreferences: Partial<IUser['preferences']>) {
  this.preferences = { ...this.preferences, ...newPreferences }
  return this.save()
}

UserSchema.methods.comparePassword = async function(candidatePassword: string) {
  const bcrypt = require('bcryptjs')
  return bcrypt.compare(candidatePassword, this.password)
}

UserSchema.methods.toJSON = function() {
  const userObject = this.toObject()
  delete userObject.password
  return userObject
}

// Static methods
UserSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() })
}

UserSchema.statics.getTopUsers = function(limit: number = 10) {
  return this.find()
    .sort({ 'stats.totalStudyTime': -1 })
    .limit(limit)
    .select('name avatar stats.totalStudyTime stats.quizzesCompleted')
}

// Pre-save middleware
UserSchema.pre('save', async function(next) {
  if (this.isModified('email')) {
    this.email = this.email.toLowerCase()
  }

  if (this.isModified('password')) {
    const bcrypt = require('bcryptjs')
    const salt = await bcrypt.genSalt(10)
    this.password = await bcrypt.hash(this.password, salt)
  }

  next()
})

// Export the model
export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema)
