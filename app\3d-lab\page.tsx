"use client"

import { Suspense, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Environment, Html } from "@react-three/drei"
import { Navigation } from "@/components/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Eye, EyeOff, RotateCcw } from "lucide-react"
import { AnatomyModel } from "@/components/anatomy-model"
import { SystemFilter } from "@/components/system-filter"

const bodySystems = [
  { id: "skeletal", name: "Skeletal System", color: "#e3f2fd", count: 206 },
  { id: "muscular", name: "Muscular System", color: "#fce4ec", count: 600 },
  { id: "nervous", name: "Nervous System", color: "#f3e5f5", count: 86 },
  { id: "circulatory", name: "Circulatory System", color: "#ffebee", count: 37 },
  { id: "respiratory", name: "Respiratory System", color: "#e8f5e8", count: 23 },
  { id: "digestive", name: "Digestive System", color: "#fff3e0", count: 32 },
]

export default function Lab3DPage() {
  const [selectedSystem, setSelectedSystem] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedOrgan, setSelectedOrgan] = useState<string | null>(null)
  const [showLabels, setShowLabels] = useState(true)

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sidebar */}
        <div className="w-80 border-r bg-card p-4 overflow-y-auto">
          <div className="space-y-4">
            <div>
              <h2 className="text-lg font-semibold mb-3">3D Anatomy Lab</h2>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search organs, bones, muscles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant={showLabels ? "default" : "outline"} size="sm" onClick={() => setShowLabels(!showLabels)}>
                {showLabels ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                Labels
              </Button>
              <Button variant="outline" size="sm">
                <RotateCcw className="h-4 w-4" />
                Reset View
              </Button>
            </div>

            <SystemFilter systems={bodySystems} selectedSystem={selectedSystem} onSystemChange={setSelectedSystem} />

            {selectedOrgan && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Heart</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    The heart is a muscular organ that pumps blood throughout the body via the circulatory system.
                  </p>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary">Circulatory</Badge>
                    <Badge variant="outline">Vital Organ</Badge>
                  </div>
                  <Button size="sm" className="w-full">
                    Ask AI About Heart
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* 3D Viewer */}
        <div className="flex-1 relative">
          <Canvas camera={{ position: [0, 0, 5], fov: 50 }}>
            <Suspense
              fallback={
                <Html center>
                  <div className="text-white">Loading 3D Model...</div>
                </Html>
              }
            >
              <Environment preset="studio" />
              <ambientLight intensity={0.5} />
              <directionalLight position={[10, 10, 5]} intensity={1} />
              <AnatomyModel selectedSystem={selectedSystem} onOrganClick={setSelectedOrgan} showLabels={showLabels} />
              <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
            </Suspense>
          </Canvas>

          <div className="absolute top-4 right-4">
            <Card className="p-3">
              <div className="text-sm font-medium">Controls</div>
              <div className="text-xs text-muted-foreground mt-1">
                <div>Left click + drag: Rotate</div>
                <div>Right click + drag: Pan</div>
                <div>Scroll: Zoom</div>
                <div>Click organs: Select</div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
