#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create and test the Learnify MongoDB database
 * This script will:
 * 1. Connect to MongoDB on localhost:27017
 * 2. Create the "learnify" database
 * 3. Create initial collections
 * 4. Insert sample data
 * 5. Test the connection
 */

const { MongoClient } = require('mongodb')

// MongoDB connection URI - hardcoded to ensure it works
const MONGODB_URI = 'mongodb://localhost:27017/learnify'

async function createLearnifyDatabase() {
  console.log('🚀 Creating Learnify Database...\n')
  
  let client
  
  try {
    // Step 1: Connect to MongoDB
    console.log('1️⃣ Connecting to MongoDB...')
    console.log('   URI:', MONGODB_URI)
    
    client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000, // 5 second timeout
    })
    
    await client.connect()
    console.log('   ✅ Connected successfully!')
    
    // Step 2: Get the learnify database
    console.log('\n2️⃣ Creating/accessing learnify database...')
    const db = client.db('learnify')
    console.log('   ✅ Database "learnify" ready!')
    
    // Step 3: Create collections with sample data
    console.log('\n3️⃣ Creating collections and sample data...')
    
    // Users collection
    const usersCollection = db.collection('users')

    // Hash password for demo user
    const bcrypt = require('bcryptjs')
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash('demo123', salt)

    const sampleUser = {
      email: '<EMAIL>',
      name: 'Demo User',
      password: hashedPassword,
      avatar: null,
      preferences: {
        theme: 'system',
        studyReminders: true,
        emailNotifications: true
      },
      stats: {
        totalStudyTime: 127,
        documentsUploaded: 1,
        quizzesCompleted: 3,
        flashcardsReviewed: 25
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    // Check if user already exists
    const existingUser = await usersCollection.findOne({ email: '<EMAIL>' })
    let userId
    
    if (!existingUser) {
      const userResult = await usersCollection.insertOne(sampleUser)
      userId = userResult.insertedId
      console.log('   ✅ Sample user created with ID:', userId)
    } else {
      userId = existingUser._id
      console.log('   ℹ️ Sample user already exists with ID:', userId)
    }
    
    // Documents collection
    const documentsCollection = db.collection('documents')
    const sampleDocument = {
      userId: userId.toString(),
      title: 'Introduction to Machine Learning',
      filename: 'ml-intro.pdf',
      fileUrl: '/uploads/sample/ml-intro.pdf',
      fileSize: 1024000,
      mimeType: 'application/pdf',
      content: '',
      extractedText: 'Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed...',
      summary: 'An introduction to the fundamental concepts of machine learning.',
      tags: ['machine learning', 'AI', 'data science'],
      subject: 'Computer Science',
      difficulty: 'intermediate',
      isProcessed: true,
      processingStatus: 'completed',
      lastAccessedAt: new Date(),
      metadata: {
        pageCount: 25,
        wordCount: 5000,
        language: 'en',
        topics: ['supervised learning', 'unsupervised learning', 'neural networks']
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    const existingDoc = await documentsCollection.findOne({ title: 'Introduction to Machine Learning' })
    let documentId
    
    if (!existingDoc) {
      const docResult = await documentsCollection.insertOne(sampleDocument)
      documentId = docResult.insertedId
      console.log('   ✅ Sample document created with ID:', documentId)
    } else {
      documentId = existingDoc._id
      console.log('   ℹ️ Sample document already exists with ID:', documentId)
    }
    
    // Flashcards collection
    const flashcardsCollection = db.collection('flashcards')
    const sampleFlashcards = [
      {
        userId: userId.toString(),
        documentId: documentId.toString(),
        front: 'What is machine learning?',
        back: 'A subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.',
        tags: ['definition', 'machine learning'],
        difficulty: 'easy',
        reviewCount: 0,
        correctCount: 0,
        lastReviewedAt: null,
        nextReviewAt: new Date(),
        isActive: true,
        spacedRepetition: {
          easeFactor: 2.5,
          interval: 1,
          repetitions: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        userId: userId.toString(),
        documentId: documentId.toString(),
        front: 'What are the three main types of machine learning?',
        back: 'Supervised learning, Unsupervised learning, and Reinforcement learning.',
        tags: ['types', 'machine learning'],
        difficulty: 'medium',
        reviewCount: 0,
        correctCount: 0,
        lastReviewedAt: null,
        nextReviewAt: new Date(),
        isActive: true,
        spacedRepetition: {
          easeFactor: 2.5,
          interval: 1,
          repetitions: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
    
    const existingFlashcards = await flashcardsCollection.countDocuments()
    if (existingFlashcards === 0) {
      await flashcardsCollection.insertMany(sampleFlashcards)
      console.log('   ✅ Sample flashcards created:', sampleFlashcards.length)
    } else {
      console.log('   ℹ️ Flashcards already exist:', existingFlashcards)
    }
    
    // Quizzes collection
    const quizzesCollection = db.collection('quizzes')
    const sampleQuiz = {
      userId: userId.toString(),
      documentId: documentId.toString(),
      title: 'Machine Learning Basics Quiz',
      description: 'Test your knowledge of basic machine learning concepts',
      questions: [
        {
          question: 'What is the primary goal of supervised learning?',
          options: [
            'To find hidden patterns in data',
            'To learn from labeled training data to make predictions',
            'To maximize rewards through trial and error',
            'To reduce the dimensionality of data'
          ],
          correctAnswer: 1,
          explanation: 'Supervised learning uses labeled training data to learn a mapping from inputs to outputs.',
          difficulty: 'medium'
        },
        {
          question: 'Which of the following is an example of unsupervised learning?',
          options: [
            'Email spam detection',
            'Image classification',
            'Customer segmentation',
            'Stock price prediction'
          ],
          correctAnswer: 2,
          explanation: 'Customer segmentation involves finding patterns in data without labeled examples.',
          difficulty: 'medium'
        }
      ],
      tags: ['machine learning', 'basics'],
      difficulty: 'medium',
      timeLimit: 300,
      attempts: [],
      isActive: true,
      settings: {
        shuffleQuestions: false,
        shuffleOptions: false,
        showCorrectAnswers: true,
        allowRetakes: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    const existingQuiz = await quizzesCollection.findOne({ title: 'Machine Learning Basics Quiz' })
    if (!existingQuiz) {
      await quizzesCollection.insertOne(sampleQuiz)
      console.log('   ✅ Sample quiz created')
    } else {
      console.log('   ℹ️ Sample quiz already exists')
    }
    
    // Step 4: Create indexes for better performance
    console.log('\n4️⃣ Creating database indexes...')
    
    await usersCollection.createIndex({ email: 1 }, { unique: true })
    await documentsCollection.createIndex({ userId: 1, createdAt: -1 })
    await documentsCollection.createIndex({ tags: 1 })
    await flashcardsCollection.createIndex({ userId: 1, nextReviewAt: 1 })
    await quizzesCollection.createIndex({ userId: 1, createdAt: -1 })
    
    console.log('   ✅ Indexes created successfully')
    
    // Step 5: Get database statistics
    console.log('\n5️⃣ Database statistics:')
    const collections = await db.listCollections().toArray()
    console.log('   📁 Collections:', collections.map(c => c.name).join(', '))
    
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments()
      console.log(`   📊 ${collection.name}: ${count} documents`)
    }
    
    console.log('\n🎉 Learnify database created successfully!')
    console.log('\n📋 Database Details:')
    console.log('   - Database Name: learnify')
    console.log('   - Connection URI: mongodb://localhost:27017/learnify')
    console.log('   - Collections: users, documents, flashcards, quizzes')
    console.log('   - Sample data: ✅ Created')
    console.log('   - Indexes: ✅ Created')
    
    console.log('\n🚀 Next Steps:')
    console.log('   1. Start your Next.js app: npm run dev')
    console.log('   2. Test the connection: http://localhost:3000/api/db/test')
    console.log('   3. Start building your learning platform!')
    
    return {
      success: true,
      database: 'learnify',
      collections: collections.map(c => c.name),
      uri: MONGODB_URI
    }
    
  } catch (error) {
    console.error('\n❌ Failed to create Learnify database:')
    console.error('   Error:', error.message)
    
    if (error.message.includes('ECONNREFUSED')) {
      console.error('\n🔧 MongoDB Connection Issue:')
      console.error('   - Make sure MongoDB is running on localhost:27017')
      console.error('   - Start MongoDB service:')
      console.error('     Windows: net start MongoDB')
      console.error('     macOS: brew services start mongodb-community')
      console.error('     Linux: sudo systemctl start mongod')
    }
    
    return {
      success: false,
      error: error.message
    }
    
  } finally {
    if (client) {
      await client.close()
      console.log('\n🔌 Database connection closed')
    }
  }
}

// Run the script
if (require.main === module) {
  createLearnifyDatabase()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ Script completed successfully!')
        process.exit(0)
      } else {
        console.log('\n❌ Script failed!')
        process.exit(1)
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error)
      process.exit(1)
    })
}

module.exports = { createLearnifyDatabase }
