import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { ArrowRight, Upload, Zap } from "lucide-react"
import Link from "next/link"

export function CTA() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <Card className="relative overflow-hidden bg-gradient-to-r from-pink-300 to-purple-300 text-white">
          <CardContent className="p-12 text-center">
            <div className="max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Zap className="h-6 w-6" />
                <span className="text-sm font-medium opacity-90">Ready to Transform Your Learning?</span>
              </div>

              <h2 className="text-3xl md:text-4xl font-bold mb-4">Start Learning Smarter Today</h2>

              <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
                Join thousands of students who have revolutionized their study experience. Upload your first PDF and
                discover the power of AI-enhanced learning.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/upload">
                  <Button size="lg" variant="secondary" className="gap-2">
                    <Upload className="h-5 w-5" />
                    Upload Your First PDF
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>

                <Link href="/study/dashboard">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-white hover:bg-white hover:text-blue-600"
                  >
                    Try Demo
                  </Button>
                </Link>
              </div>

              <div className="mt-8 text-sm opacity-75">
                Free to start • No credit card required • Process up to 5 PDFs
              </div>
            </div>
          </CardContent>

          {/* Background decoration with pastel colors */}
          <div className="absolute inset-0 bg-gradient-to-r from-pink-300/20 to-purple-300/20" />
          <div className="absolute -top-24 -right-24 w-48 h-48 bg-white/10 rounded-full blur-3xl" />
          <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-white/10 rounded-full blur-3xl" />
        </Card>
      </div>
    </section>
  )
}
