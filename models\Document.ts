import mongoose, { Document, Schema } from 'mongoose'

export interface IDocument extends Document {
  userId: string
  title: string
  filename: string
  fileUrl: string
  fileSize: number
  mimeType: string
  content: string
  extractedText: string
  summary: string
  tags: string[]
  subject: string
  difficulty: 'easy' | 'medium' | 'hard'
  isProcessed: boolean
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed'
  lastAccessedAt: Date
  metadata: {
    pageCount?: number
    wordCount?: number
    language?: string
    topics?: string[]
  }
  createdAt: Date
  updatedAt: Date
}

const DocumentSchema = new Schema<IDocument>({
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    index: true
  },
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  filename: {
    type: String,
    required: [true, 'Filename is required'],
    trim: true
  },
  fileUrl: {
    type: String,
    required: [true, 'File URL is required']
  },
  fileSize: {
    type: Number,
    required: [true, 'File size is required'],
    min: [0, 'File size cannot be negative']
  },
  mimeType: {
    type: String,
    required: [true, 'MIME type is required']
  },
  content: {
    type: String,
    default: ''
  },
  extractedText: {
    type: String,
    default: ''
  },
  summary: {
    type: String,
    default: '',
    maxlength: [1000, 'Summary cannot be more than 1000 characters']
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  subject: {
    type: String,
    required: [true, 'Subject is required'],
    trim: true,
    maxlength: [100, 'Subject cannot be more than 100 characters']
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  isProcessed: {
    type: Boolean,
    default: false
  },
  processingStatus: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  metadata: {
    pageCount: {
      type: Number,
      min: [0, 'Page count cannot be negative']
    },
    wordCount: {
      type: Number,
      min: [0, 'Word count cannot be negative']
    },
    language: {
      type: String,
      default: 'en'
    },
    topics: [{
      type: String,
      trim: true
    }]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
DocumentSchema.index({ userId: 1, createdAt: -1 })
DocumentSchema.index({ tags: 1 })
DocumentSchema.index({ subject: 1 })
DocumentSchema.index({ difficulty: 1 })
DocumentSchema.index({ processingStatus: 1 })
DocumentSchema.index({ title: 'text', extractedText: 'text', summary: 'text' })

// Virtual for file size in human readable format
DocumentSchema.virtual('fileSizeFormatted').get(function() {
  const bytes = this.fileSize
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
})

// Virtual for reading time estimate (assuming 200 words per minute)
DocumentSchema.virtual('estimatedReadingTime').get(function() {
  if (!this.metadata.wordCount) return 0
  return Math.ceil(this.metadata.wordCount / 200)
})

// Methods
DocumentSchema.methods.updateLastAccessed = function() {
  this.lastAccessedAt = new Date()
  return this.save()
}

DocumentSchema.methods.addTag = function(tag: string) {
  const normalizedTag = tag.toLowerCase().trim()
  if (!this.tags.includes(normalizedTag)) {
    this.tags.push(normalizedTag)
    return this.save()
  }
  return Promise.resolve(this)
}

DocumentSchema.methods.removeTag = function(tag: string) {
  const normalizedTag = tag.toLowerCase().trim()
  this.tags = this.tags.filter(t => t !== normalizedTag)
  return this.save()
}

DocumentSchema.methods.updateProcessingStatus = function(status: IDocument['processingStatus']) {
  this.processingStatus = status
  if (status === 'completed') {
    this.isProcessed = true
  }
  return this.save()
}

// Static methods
DocumentSchema.statics.findByUserId = function(userId: string, options: any = {}) {
  const { limit = 20, skip = 0, sort = { createdAt: -1 } } = options
  return this.find({ userId })
    .sort(sort)
    .limit(limit)
    .skip(skip)
}

DocumentSchema.statics.findByTags = function(tags: string[], userId?: string) {
  const query: any = { tags: { $in: tags.map(tag => tag.toLowerCase()) } }
  if (userId) query.userId = userId
  return this.find(query).sort({ createdAt: -1 })
}

DocumentSchema.statics.searchDocuments = function(searchTerm: string, userId?: string) {
  const query: any = {
    $text: { $search: searchTerm }
  }
  if (userId) query.userId = userId
  return this.find(query, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } })
}

// Export the model
export default mongoose.models.Document || mongoose.model<IDocument>('Document', DocumentSchema)
