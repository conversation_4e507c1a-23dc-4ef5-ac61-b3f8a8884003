# Learnify Database Documentation

## Overview

The Learnify project uses MongoDB as its primary database with Mongoose as the ODM (Object Document Mapper). The database is designed to support a comprehensive learning platform with users, documents, flashcards, and quizzes.

## Database Setup

### Prerequisites

- MongoDB installed and running on `localhost:27017`
- Node.js and npm/pnpm installed
- All project dependencies installed (`npm install` or `pnpm install`)

### Quick Start

1. **Create the database and sample data:**
   ```bash
   npm run db:create
   ```

2. **Test the database connection:**
   ```bash
   npm run test:db
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Test all API endpoints:**
   ```bash
   npm run test:server
   ```

## Database Schema

### Collections

#### 1. Users Collection
Stores user information, preferences, and statistics.

**Schema:**
- `email` (String, unique, required) - User's email address
- `name` (String, required) - User's display name
- `avatar` (String, optional) - URL to user's avatar image
- `preferences` (Object) - User preferences
  - `theme` (String) - UI theme: 'light', 'dark', or 'system'
  - `studyReminders` (Boolean) - Enable study reminders
  - `emailNotifications` (Boolean) - Enable email notifications
- `stats` (Object) - User statistics
  - `totalStudyTime` (Number) - Total study time in minutes
  - `documentsUploaded` (Number) - Number of documents uploaded
  - `quizzesCompleted` (Number) - Number of quizzes completed
  - `flashcardsReviewed` (Number) - Number of flashcards reviewed
- `createdAt` (Date) - Account creation timestamp
- `updatedAt` (Date) - Last update timestamp

**Indexes:**
- `email` (unique)
- `createdAt` (descending)

#### 2. Documents Collection
Stores uploaded documents and their metadata.

**Schema:**
- `userId` (String, required) - Reference to user who uploaded the document
- `title` (String, required) - Document title
- `filename` (String, required) - Original filename
- `fileUrl` (String, required) - URL to the stored file
- `fileSize` (Number, required) - File size in bytes
- `mimeType` (String, required) - MIME type of the file
- `content` (String) - Raw file content
- `extractedText` (String) - Extracted text content
- `summary` (String) - AI-generated summary
- `tags` (Array of Strings) - Document tags
- `subject` (String, required) - Subject category
- `difficulty` (String) - Difficulty level: 'easy', 'medium', or 'hard'
- `isProcessed` (Boolean) - Whether the document has been processed
- `processingStatus` (String) - Processing status: 'pending', 'processing', 'completed', or 'failed'
- `lastAccessedAt` (Date) - Last access timestamp
- `metadata` (Object) - Additional metadata
  - `pageCount` (Number) - Number of pages
  - `wordCount` (Number) - Number of words
  - `language` (String) - Document language
  - `topics` (Array of Strings) - Extracted topics
- `createdAt` (Date) - Upload timestamp
- `updatedAt` (Date) - Last update timestamp

**Indexes:**
- `userId` + `createdAt` (compound, descending)
- `tags`
- `subject`
- `difficulty`
- `processingStatus`
- Text search on `title`, `extractedText`, and `summary`

#### 3. Flashcards Collection
Stores flashcards with spaced repetition algorithm support.

**Schema:**
- `userId` (String, required) - Reference to user who owns the flashcard
- `documentId` (String, required) - Reference to source document
- `front` (String, required) - Front side content (question)
- `back` (String, required) - Back side content (answer)
- `tags` (Array of Strings) - Flashcard tags
- `difficulty` (String) - Difficulty level: 'easy', 'medium', or 'hard'
- `reviewCount` (Number) - Number of times reviewed
- `correctCount` (Number) - Number of correct answers
- `lastReviewedAt` (Date) - Last review timestamp
- `nextReviewAt` (Date) - Next scheduled review
- `isActive` (Boolean) - Whether the flashcard is active
- `spacedRepetition` (Object) - Spaced repetition algorithm data
  - `easeFactor` (Number) - Ease factor for SM-2 algorithm
  - `interval` (Number) - Current interval in days
  - `repetitions` (Number) - Number of successful repetitions
- `createdAt` (Date) - Creation timestamp
- `updatedAt` (Date) - Last update timestamp

**Indexes:**
- `userId` + `nextReviewAt` (compound)
- `documentId`
- `tags`
- `difficulty`
- `isActive`

#### 4. Quizzes Collection
Stores quizzes with questions and attempt tracking.

**Schema:**
- `userId` (String, required) - Reference to user who created the quiz
- `documentId` (String, required) - Reference to source document
- `title` (String, required) - Quiz title
- `description` (String) - Quiz description
- `questions` (Array of Objects) - Quiz questions
  - `question` (String) - Question text
  - `options` (Array of Strings) - Answer options
  - `correctAnswer` (Number) - Index of correct answer
  - `explanation` (String) - Explanation of the answer
  - `difficulty` (String) - Question difficulty
- `tags` (Array of Strings) - Quiz tags
- `difficulty` (String) - Overall quiz difficulty
- `timeLimit` (Number) - Time limit in seconds
- `attempts` (Array of Objects) - Quiz attempts
  - `attemptId` (String) - Unique attempt identifier
  - `startedAt` (Date) - Attempt start time
  - `completedAt` (Date) - Attempt completion time
  - `answers` (Array of Numbers) - User's answers
  - `score` (Number) - Final score (0-100)
  - `timeSpent` (Number) - Time spent in seconds
  - `isCompleted` (Boolean) - Whether attempt is completed
- `isActive` (Boolean) - Whether the quiz is active
- `settings` (Object) - Quiz settings
  - `shuffleQuestions` (Boolean) - Shuffle question order
  - `shuffleOptions` (Boolean) - Shuffle answer options
  - `showCorrectAnswers` (Boolean) - Show correct answers after completion
  - `allowRetakes` (Boolean) - Allow multiple attempts
- `createdAt` (Date) - Creation timestamp
- `updatedAt` (Date) - Last update timestamp

**Indexes:**
- `userId` + `createdAt` (compound, descending)
- `documentId`
- `tags`
- `difficulty`
- `isActive`

## API Endpoints

### Database Test
- `GET /api/db/test` - Test database connection and get statistics

### Users
- `GET /api/users` - Get all users (with pagination)
- `GET /api/users?email={email}` - Get user by email
- `POST /api/users` - Create new user
- `PUT /api/users` - Update user
- `DELETE /api/users?email={email}` - Delete user

### Documents
- `GET /api/documents` - Get documents (with filtering and search)
- `POST /api/documents` - Upload new document
- `PUT /api/documents` - Update document
- `DELETE /api/documents?id={id}` - Delete document

### Flashcards
- `GET /api/flashcards` - Get flashcards (with filtering)
- `POST /api/flashcards` - Create new flashcard
- `PUT /api/flashcards` - Update flashcard
- `DELETE /api/flashcards?id={id}` - Delete flashcard
- `GET /api/flashcards/review?userId={userId}` - Get due flashcards for review
- `POST /api/flashcards/review` - Record flashcard review

### Quizzes
- `GET /api/quizzes` - Get quizzes (with filtering)
- `POST /api/quizzes` - Create new quiz
- `PUT /api/quizzes` - Update quiz
- `DELETE /api/quizzes?id={id}` - Delete quiz
- `POST /api/quizzes/attempt` - Start, answer, or complete quiz attempt
- `GET /api/quizzes/attempt?quizId={id}` - Get quiz attempt data

### Statistics
- `GET /api/stats?userId={userId}&type={type}` - Get user statistics
  - Types: 'overview', 'documents', 'flashcards', 'quizzes'

## Testing

### Available Test Scripts

```bash
# Test database connection and models
npm run test:db

# Test API endpoints (requires server to be running)
npm run test:server

# Run all tests
npm run test:all
```

### Manual Testing

1. Start the development server: `npm run dev`
2. Test database endpoint: `curl http://localhost:3000/api/db/test`
3. Test user endpoint: `curl http://localhost:3000/api/users`

## Sample Data

The database comes pre-populated with sample data:

- **Demo User**: `<EMAIL>`
- **Sample Document**: "Introduction to Machine Learning"
- **Sample Flashcards**: 2 machine learning flashcards
- **Sample Quiz**: "Machine Learning Basics Quiz"

## Environment Variables

Create a `.env.local` file with:

```env
MONGODB_URI=mongodb://localhost:27017/learnify
```

If not provided, the system defaults to `mongodb://localhost:27017/learnify`.

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure MongoDB is running on localhost:27017
2. **Duplicate Index Warnings**: These have been resolved in the current schema
3. **Server Not Starting**: Check for port conflicts on port 3000

### MongoDB Commands

```bash
# Start MongoDB (Windows)
net start MongoDB

# Start MongoDB (macOS with Homebrew)
brew services start mongodb-community

# Start MongoDB (Linux)
sudo systemctl start mongod
```

## Development Notes

- All models use Mongoose for schema validation and middleware
- Spaced repetition algorithm (SM-2) is implemented for flashcards
- Text search is enabled on documents
- Soft deletes are used for flashcards and quizzes (isActive flag)
- All timestamps are automatically managed by Mongoose
