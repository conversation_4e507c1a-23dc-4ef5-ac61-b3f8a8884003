import { Navigation } from "@/components/navigation"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Heart, Brain, Users, Zap, Mail, Github, Twitter, Lightbulb, Target, Award } from "lucide-react"

const teamMembers = [
  {
    name: "Dr. <PERSON>",
    role: "Medical Advisor",
    bio: "Anatomy professor with 15+ years of teaching experience",
    avatar: "/placeholder.svg?height=100&width=100",
  },
  {
    name: "<PERSON>",
    role: "Lead Developer",
    bio: "Full-stack developer specializing in 3D web applications",
    avatar: "/placeholder.svg?height=100&width=100",
  },
  {
    name: "<PERSON>",
    role: "AI Engineer",
    bio: "Machine learning expert focused on educational AI systems",
    avatar: "/placeholder.svg?height=100&width=100",
  },
  {
    name: "<PERSON>",
    role: "UX Designer",
    bio: "Designer passionate about creating intuitive learning experiences",
    avatar: "/placeholder.svg?height=100&width=100",
  },
]

const technologies = [
  { name: "Next.js", category: "Frontend" },
  { name: "React Three Fiber", category: "3D Graphics" },
  { name: "OpenAI GPT-4", category: "AI" },
  { name: "WebXR", category: "AR/VR" },
  { name: "Supabase", category: "Backend" },
  { name: "Tailwind CSS", category: "Styling" },
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Heart className="h-8 w-8 text-red-500" />
            <Brain className="h-8 w-8 text-purple-500" />
          </div>
          <h1 className="text-4xl font-bold mb-4">About Bolt AI Anatomy</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Revolutionizing anatomy education through cutting-edge AI, immersive 3D visualization, and augmented reality
            technology.
          </p>
        </div>

        {/* Mission Section */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-500" />
                Our Mission
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                To make anatomy learning accessible, engaging, and effective for students, educators, and healthcare
                professionals worldwide through innovative technology.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                Our Vision
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                A world where complex anatomical concepts are understood intuitively through interactive 3D models, AI
                assistance, and immersive AR experiences.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-green-500" />
                Our Impact
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Empowering the next generation of healthcare professionals with tools that enhance learning outcomes and
                improve patient care through better education.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Features Highlight */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-center">What Makes Us Different</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Brain className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">AI-Powered Learning</h3>
                <p className="text-sm text-muted-foreground">
                  Get instant answers and personalized explanations from our advanced AI assistant
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Interactive 3D Models</h3>
                <p className="text-sm text-muted-foreground">
                  Explore detailed anatomical structures with fully interactive 3D visualization
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Heart className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">AR Integration</h3>
                <p className="text-sm text-muted-foreground">
                  Project anatomical models into your real environment for immersive learning
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Adaptive Quizzes</h3>
                <p className="text-sm text-muted-foreground">
                  Test your knowledge with AI-generated quizzes that adapt to your learning pace
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Section */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-center mb-8">Meet Our Team</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {teamMembers.map((member, index) => (
              <Card key={index}>
                <CardContent className="p-6 text-center">
                  <img
                    src={member.avatar || "/placeholder.svg"}
                    alt={member.name}
                    className="w-20 h-20 rounded-full mx-auto mb-4"
                  />
                  <h3 className="font-semibold mb-1">{member.name}</h3>
                  <p className="text-sm text-primary mb-2">{member.role}</p>
                  <p className="text-xs text-muted-foreground">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Technology Stack */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-center">Built With Cutting-Edge Technology</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap justify-center gap-2">
              {technologies.map((tech, index) => (
                <Badge key={index} variant="secondary" className="text-sm">
                  {tech.name}
                </Badge>
              ))}
            </div>
            <p className="text-center text-muted-foreground mt-4">
              We leverage the latest in web technology, AI, and 3D graphics to deliver an unparalleled learning
              experience.
            </p>
          </CardContent>
        </Card>

        {/* Contact Section */}
        <div className="grid md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Get in Touch
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input id="name" placeholder="Your name" />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" />
              </div>
              <div>
                <Label htmlFor="message">Message</Label>
                <Textarea id="message" placeholder="Tell us about your feedback or partnership ideas..." />
              </div>
              <Button className="w-full">Send Message</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Connect With Us</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Follow our journey and stay updated with the latest features and improvements.
              </p>

              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Github className="mr-2 h-4 w-4" />
                  View on GitHub
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Twitter className="mr-2 h-4 w-4" />
                  Follow on Twitter
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Mail className="mr-2 h-4 w-4" />
                  <EMAIL>
                </Button>
              </div>

              <div className="pt-4 border-t">
                <h4 className="font-semibold mb-2">Partnership Opportunities</h4>
                <p className="text-sm text-muted-foreground">
                  Interested in partnering with us? We're open to collaborations with educational institutions,
                  healthcare organizations, and technology companies.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
