@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 340 82% 85%;
    --primary-foreground: 340 82% 25%;
    --secondary: 200 50% 90%;
    --secondary-foreground: 200 50% 30%;
    --muted: 220 14% 96%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 280 65% 90%;
    --accent-foreground: 280 65% 30%;
    --destructive: 0 84.2% 85%;
    --destructive-foreground: 0 84.2% 25%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 340 82% 85%;
    --radius: 0.75rem;
    --chart-1: 340 82% 85%;
    --chart-2: 200 50% 85%;
    --chart-3: 280 65% 85%;
    --chart-4: 60 80% 85%;
    --chart-5: 120 60% 85%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 340 82% 75%;
    --primary-foreground: 340 82% 15%;
    --secondary: 200 50% 80%;
    --secondary-foreground: 200 50% 20%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 280 65% 80%;
    --accent-foreground: 280 65% 20%;
    --destructive: 0 84.2% 75%;
    --destructive-foreground: 0 84.2% 15%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 340 82% 75%;
    --chart-1: 340 82% 75%;
    --chart-2: 200 50% 75%;
    --chart-3: 280 65% 75%;
    --chart-4: 60 80% 75%;
    --chart-5: 120 60% 75%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Flashcard 3D flip animation */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Smooth animations */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Loading animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gamification animations */
@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-bounce {
  animation: bounce 1s ease infinite;
}

/* XP gain animation */
@keyframes xp-gain {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-40px) scale(0.8);
    opacity: 0;
  }
}

.animate-xp-gain {
  animation: xp-gain 1s ease-out forwards;
}
