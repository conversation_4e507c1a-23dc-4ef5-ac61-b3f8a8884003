import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Flashcard } from '@/models'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const documentId = searchParams.get('documentId')
    const tags = searchParams.get('tags')?.split(',').filter(Boolean)
    const difficulty = searchParams.get('difficulty')
    const dueOnly = searchParams.get('dueOnly') === 'true'
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = parseInt(searchParams.get('skip') || '0')
    
    let query: any = { isActive: true }
    
    if (userId) {
      query.userId = userId
    }
    
    if (documentId) {
      query.documentId = documentId
    }
    
    if (difficulty) {
      query.difficulty = difficulty
    }
    
    if (tags && tags.length > 0) {
      query.tags = { $in: tags.map(tag => tag.toLowerCase()) }
    }
    
    if (dueOnly) {
      query.nextReviewAt = { $lte: new Date() }
    }
    
    const flashcards = await Flashcard.find(query)
      .sort(dueOnly ? { nextReviewAt: 1 } : { createdAt: -1 })
      .limit(limit)
      .skip(skip)
    
    const total = await Flashcard.countDocuments(query)
    
    return NextResponse.json({
      success: true,
      data: flashcards,
      pagination: {
        total,
        limit,
        skip,
        hasMore: skip + limit < total
      }
    })
    
  } catch (error) {
    console.error('Error fetching flashcards:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch flashcards' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const {
      userId,
      documentId,
      front,
      back,
      tags,
      difficulty
    } = body
    
    // Validate required fields
    if (!userId || !documentId || !front || !back) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    // Create new flashcard
    const flashcardData = {
      userId,
      documentId,
      front,
      back,
      tags: tags || [],
      difficulty: difficulty || 'medium'
    }
    
    const flashcard = new Flashcard(flashcardData)
    await flashcard.save()
    
    return NextResponse.json({
      success: true,
      message: 'Flashcard created successfully',
      data: flashcard
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error creating flashcard:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to create flashcard' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Flashcard ID is required' },
        { status: 400 }
      )
    }
    
    const flashcard = await Flashcard.findById(id)
    if (!flashcard) {
      return NextResponse.json(
        { success: false, message: 'Flashcard not found' },
        { status: 404 }
      )
    }
    
    // Update flashcard fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        flashcard[key] = updateData[key]
      }
    })
    
    await flashcard.save()
    
    return NextResponse.json({
      success: true,
      message: 'Flashcard updated successfully',
      data: flashcard
    })
    
  } catch (error) {
    console.error('Error updating flashcard:', error)
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to update flashcard' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Flashcard ID is required' },
        { status: 400 }
      )
    }
    
    const flashcard = await Flashcard.findById(id)
    if (!flashcard) {
      return NextResponse.json(
        { success: false, message: 'Flashcard not found' },
        { status: 404 }
      )
    }
    
    // Soft delete by setting isActive to false
    flashcard.isActive = false
    await flashcard.save()
    
    return NextResponse.json({
      success: true,
      message: 'Flashcard deleted successfully'
    })
    
  } catch (error) {
    console.error('Error deleting flashcard:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to delete flashcard' },
      { status: 500 }
    )
  }
}
