import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Flashcard } from '@/models'

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { flashcardId, isCorrect, quality } = body
    
    // Validate required fields
    if (!flashcardId || typeof isCorrect !== 'boolean') {
      return NextResponse.json(
        { success: false, message: 'Flashcard ID and isCorrect are required' },
        { status: 400 }
      )
    }
    
    const flashcard = await Flashcard.findById(flashcardId)
    if (!flashcard) {
      return NextResponse.json(
        { success: false, message: 'Flashcard not found' },
        { status: 404 }
      )
    }
    
    // Record the review
    await flashcard.recordReview(isCorrect, quality || (isCorrect ? 4 : 2))
    
    return NextResponse.json({
      success: true,
      message: 'Review recorded successfully',
      data: {
        flashcard,
        nextReviewAt: flashcard.nextReviewAt,
        successRate: flashcard.successRate,
        reviewCount: flashcard.reviewCount
      }
    })
    
  } catch (error) {
    console.error('Error recording flashcard review:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to record review' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      )
    }
    
    // Get flashcards due for review
    const dueFlashcards = await Flashcard.findDueForReview(userId, limit)
    
    // Get study stats
    const stats = await Flashcard.getStudyStats(userId)
    
    return NextResponse.json({
      success: true,
      data: {
        dueFlashcards,
        stats: stats[0] || {
          totalCards: 0,
          dueCards: 0,
          averageSuccessRate: 0
        }
      }
    })
    
  } catch (error) {
    console.error('Error fetching review data:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch review data' },
      { status: 500 }
    )
  }
}
