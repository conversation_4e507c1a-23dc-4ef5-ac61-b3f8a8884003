import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Document, StudyPlan, Flashcard, Quiz } from '@/models'
import StudyPlanModel from '@/models/StudyPlan'

// Helper function to generate study topics from PDF content
function generateStudyTopics(extractedText: string, analysisData: any): any[] {
  const { mainTopics, complexity, estimatedStudyTime } = analysisData
  const topics = []
  
  // Split content into logical sections
  const sections = extractedText.split(/\n\s*\n/).filter(section => section.trim().length > 100)
  const wordsPerSection = sections.map(section => section.split(/\s+/).length)
  
  // Create topics based on main topics identified
  mainTopics.forEach((topicTitle: string, index: number) => {
    const topicId = `topic-${index + 1}`
    const sectionText = sections[index] || sections[0] || extractedText.substring(0, 1000)
    
    // Estimate duration based on content length and complexity
    const wordCount = wordsPerSection[index] || 500
    const baseDuration = Math.max(30, Math.min(90, (wordCount / 200) * 60)) // 30-90 minutes
    const complexityMultiplier = 1 + (complexity - 5) * 0.1
    const estimatedDuration = Math.round(baseDuration * complexityMultiplier)
    
    // Extract key points (simplified approach)
    const sentences = sectionText.split(/[.!?]+/).filter(s => s.trim().length > 20)
    const keyPoints = sentences
      .slice(0, 5)
      .map(sentence => sentence.trim())
      .filter(point => point.length > 10 && point.length < 200)
    
    // Generate summary
    const summary = sentences.slice(0, 3).join('. ').substring(0, 500) + '...'
    
    // Generate motivation tip
    const motivationTips = [
      "Break this topic into smaller chunks and take notes as you go!",
      "Try to connect this concept to real-world examples you know.",
      "Create mental maps to visualize the relationships between ideas.",
      "Teach this concept to someone else to reinforce your understanding.",
      "Use the Feynman technique: explain it in simple terms.",
      "Take breaks every 25 minutes to maintain focus and retention.",
      "Review previous topics before diving into this new material."
    ]
    const motivationTip = motivationTips[index % motivationTips.length]
    
    // Determine prerequisites
    const prerequisites = index > 0 ? [`topic-${index}`] : []
    
    topics.push({
      topicId,
      title: topicTitle,
      description: `Study session covering ${topicTitle.toLowerCase()} concepts and applications.`,
      estimatedDuration,
      difficulty: complexity > 7 ? 'hard' : complexity > 4 ? 'medium' : 'easy',
      prerequisites,
      keyPoints: keyPoints.length > 0 ? keyPoints : [
        `Understand the core concepts of ${topicTitle}`,
        `Learn the practical applications`,
        `Review key terminology and definitions`
      ],
      summary,
      motivationTip,
      pageReferences: [Math.floor(index * 10) + 1], // Simplified page reference
      isCompleted: false
    })
  })
  
  return topics
}

// Helper function to generate study schedule
function generateStudySchedule(topics: any[], preferences: any): any[] {
  const schedule = []
  const { dailyStudyTime, preferredDays, bufferDays, revisionSlots } = preferences
  
  let currentDate = new Date()
  currentDate.setHours(0, 0, 0, 0)
  
  // If no preferred days specified, use weekdays
  const studyDays = preferredDays.length > 0 ? preferredDays : [1, 2, 3, 4, 5] // Mon-Fri
  
  let topicIndex = 0
  let daysSinceLastStudy = 0
  
  while (topicIndex < topics.length) {
    const dayOfWeek = currentDate.getDay()
    
    if (studyDays.includes(dayOfWeek)) {
      const topic = topics[topicIndex]
      const scheduledDuration = Math.min(topic.estimatedDuration, dailyStudyTime)
      
      schedule.push({
        date: new Date(currentDate),
        topicId: topic.topicId,
        scheduledDuration,
        isCompleted: false
      })
      
      // If topic duration exceeds daily study time, split it
      if (topic.estimatedDuration > dailyStudyTime) {
        topic.estimatedDuration -= dailyStudyTime
      } else {
        topicIndex++
        daysSinceLastStudy = 0
      }
      
      // Add buffer day if enabled and it's been a while
      if (bufferDays && daysSinceLastStudy > 3 && topicIndex < topics.length) {
        currentDate.setDate(currentDate.getDate() + 1)
        continue
      }
      
      // Add revision slot if enabled
      if (revisionSlots && topicIndex > 0 && topicIndex % 3 === 0) {
        currentDate.setDate(currentDate.getDate() + 1)
        if (studyDays.includes(currentDate.getDay())) {
          schedule.push({
            date: new Date(currentDate),
            topicId: 'revision',
            scheduledDuration: dailyStudyTime,
            isCompleted: false
          })
        }
      }
    }
    
    currentDate.setDate(currentDate.getDate() + 1)
    daysSinceLastStudy++
    
    // Prevent infinite loop
    if (daysSinceLastStudy > 14) break
  }
  
  return schedule
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { documentId, userId, preferences = {} } = body
    
    if (!documentId || !userId) {
      return NextResponse.json(
        { success: false, message: 'Document ID and User ID are required' },
        { status: 400 }
      )
    }
    
    // Get document
    const document = await Document.findById(documentId)
    if (!document) {
      return NextResponse.json(
        { success: false, message: 'Document not found' },
        { status: 404 }
      )
    }
    
    if (document.userId !== userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized access to document' },
        { status: 403 }
      )
    }
    
    // Check if study plan already exists
    const existingPlan = await StudyPlanModel.findOne({ documentId, userId, isActive: true })
    if (existingPlan) {
      return NextResponse.json(
        { success: false, message: 'Study plan already exists for this document' },
        { status: 409 }
      )
    }
    
    // Set default preferences
    const studyPreferences = {
      dailyStudyTime: preferences.dailyStudyTime || 60,
      preferredDays: preferences.preferredDays || [1, 2, 3, 4, 5],
      bufferDays: preferences.bufferDays !== false,
      revisionSlots: preferences.revisionSlots !== false
    }
    
    // Generate study topics
    const topics = generateStudyTopics(
      document.extractedText,
      document.metadata.analysisData
    )
    
    // Generate study schedule
    const schedule = generateStudySchedule(topics, studyPreferences)
    
    // Calculate total estimated hours
    const totalEstimatedHours = topics.reduce((sum, topic) => sum + topic.estimatedDuration, 0) / 60
    
    // Calculate estimated end date
    const estimatedEndDate = new Date()
    if (schedule.length > 0) {
      const lastScheduleItem = schedule[schedule.length - 1]
      estimatedEndDate.setTime(lastScheduleItem.date.getTime())
    } else {
      estimatedEndDate.setDate(estimatedEndDate.getDate() + 30) // Default 30 days
    }
    
    // Create study plan
    const studyPlanData = {
      userId,
      documentId,
      title: `Study Plan: ${document.title}`,
      description: `Intelligent study plan for ${document.title} with ${topics.length} topics over ${Math.ceil(totalEstimatedHours)} hours.`,
      totalEstimatedHours,
      difficulty: document.difficulty,
      subject: document.subject,
      topics,
      schedule,
      preferences: studyPreferences,
      progress: {
        completedTopics: 0,
        totalTopics: topics.length,
        completedHours: 0,
        totalHours: totalEstimatedHours,
        currentStreak: 0
      },
      isActive: true,
      startDate: new Date(),
      estimatedEndDate
    }
    
    const studyPlan = new StudyPlanModel(studyPlanData)
    await studyPlan.save()
    
    // Update document to indicate it has a study plan
    await Document.findByIdAndUpdate(documentId, {
      hasStudyPlan: true,
      studyPlanId: studyPlan._id
    })
    
    return NextResponse.json({
      success: true,
      message: 'Study plan generated successfully',
      data: {
        studyPlan: {
          id: studyPlan._id,
          title: studyPlan.title,
          description: studyPlan.description,
          totalTopics: studyPlan.progress.totalTopics,
          totalHours: studyPlan.totalEstimatedHours,
          estimatedEndDate: studyPlan.estimatedEndDate,
          topics: studyPlan.topics.map(topic => ({
            id: topic.topicId,
            title: topic.title,
            duration: topic.estimatedDuration,
            difficulty: topic.difficulty
          })),
          todaysSchedule: studyPlan.getTodaysSchedule(),
          upcomingSchedule: studyPlan.getUpcomingSchedule(7)
        }
      }
    }, { status: 201 })
    
  } catch (error) {
    console.error('Study plan generation error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to generate study plan' },
      { status: 500 }
    )
  }
}
