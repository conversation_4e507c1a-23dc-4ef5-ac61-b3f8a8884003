import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Document, StudyPlan, Flashcard, Quiz } from '@/models'
import StudyPlanModel from '@/models/StudyPlan'

// Helper function to generate flashcards for a topic
function generateFlashcardsForTopic(topicTitle: string, content: string, difficulty: string): any[] {
  const flashcards = []
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20)
  
  // Generate different types of flashcards
  const flashcardTypes = [
    {
      type: 'definition',
      front: `What is ${topicTitle}?`,
      back: sentences.slice(0, 2).join('. ').trim() || `${topicTitle} is a key concept that requires understanding of its fundamental principles and applications.`
    },
    {
      type: 'concept',
      front: `Explain the main idea behind ${topicTitle}`,
      back: sentences.slice(1, 3).join('. ').trim() || `The main idea involves understanding the core principles and how they apply in practice.`
    },
    {
      type: 'application',
      front: `How is ${topicTitle} applied in practice?`,
      back: sentences.slice(2, 4).join('. ').trim() || `This concept is applied through various methods and techniques in real-world scenarios.`
    },
    {
      type: 'comparison',
      front: `What are the key characteristics of ${topicTitle}?`,
      back: sentences.slice(0, 3).join('. ').trim() || `Key characteristics include fundamental properties and distinguishing features.`
    },
    {
      type: 'example',
      front: `Give an example related to ${topicTitle}`,
      back: sentences.slice(3, 5).join('. ').trim() || `Examples can be found in various contexts where this concept is relevant.`
    }
  ]
  
  // Select 5-8 flashcards based on difficulty
  const numCards = difficulty === 'hard' ? 8 : difficulty === 'medium' ? 6 : 5
  
  for (let i = 0; i < Math.min(numCards, flashcardTypes.length); i++) {
    const card = flashcardTypes[i]
    if (card.back.length > 20) { // Only include cards with meaningful content
      flashcards.push({
        front: card.front,
        back: card.back,
        tags: [topicTitle.toLowerCase().replace(/\s+/g, '-')],
        difficulty
      })
    }
  }
  
  return flashcards
}

// Helper function to generate quiz questions for a topic
function generateQuizForTopic(topicTitle: string, content: string, difficulty: string): any[] {
  const questions = []
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20)
  
  // Generate different types of questions
  const questionTemplates = [
    {
      type: 'mcq',
      question: `Which of the following best describes ${topicTitle}?`,
      options: [
        sentences[0]?.trim() || 'A fundamental concept in the field',
        'An outdated theory with no practical application',
        'A simple process with no complexity',
        'An unrelated topic'
      ],
      correctAnswer: 0,
      explanation: `${topicTitle} is correctly described by the first option as it represents the core understanding of this concept.`
    },
    {
      type: 'true-false',
      question: `True or False: ${topicTitle} is an important concept to understand.`,
      options: ['True', 'False'],
      correctAnswer: 0,
      explanation: `True. ${topicTitle} is indeed an important concept that forms the foundation for further learning.`
    },
    {
      type: 'mcq',
      question: `What is the primary purpose of studying ${topicTitle}?`,
      options: [
        'To understand its practical applications',
        'To memorize definitions only',
        'To avoid the topic entirely',
        'To confuse other concepts'
      ],
      correctAnswer: 0,
      explanation: 'Understanding practical applications helps in grasping the real-world relevance of the concept.'
    },
    {
      type: 'mcq',
      question: `Which approach is most effective when learning about ${topicTitle}?`,
      options: [
        'Passive reading only',
        'Active engagement and practice',
        'Ignoring the details',
        'Rushing through the material'
      ],
      correctAnswer: 1,
      explanation: 'Active engagement and practice lead to better understanding and retention of the material.'
    },
    {
      type: 'short-answer',
      question: `Briefly explain why ${topicTitle} is relevant in this subject.`,
      options: [],
      correctAnswer: 0,
      explanation: `${topicTitle} is relevant because it provides foundational knowledge that connects to other concepts in the field.`
    }
  ]
  
  // Select 5-8 questions based on difficulty
  const numQuestions = difficulty === 'hard' ? 8 : difficulty === 'medium' ? 6 : 5
  
  for (let i = 0; i < Math.min(numQuestions, questionTemplates.length); i++) {
    const template = questionTemplates[i]
    questions.push({
      question: template.question,
      options: template.options,
      correctAnswer: template.correctAnswer,
      explanation: template.explanation,
      difficulty
    })
  }
  
  return questions
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { studyPlanId, topicId, userId } = body
    
    if (!studyPlanId || !topicId || !userId) {
      return NextResponse.json(
        { success: false, message: 'Study plan ID, topic ID, and user ID are required' },
        { status: 400 }
      )
    }
    
    // Get study plan
    const studyPlan = await StudyPlanModel.findById(studyPlanId)
    if (!studyPlan) {
      return NextResponse.json(
        { success: false, message: 'Study plan not found' },
        { status: 404 }
      )
    }
    
    if (studyPlan.userId !== userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized access to study plan' },
        { status: 403 }
      )
    }
    
    // Find the specific topic
    const topic = studyPlan.topics.find(t => t.topicId === topicId)
    if (!topic) {
      return NextResponse.json(
        { success: false, message: 'Topic not found in study plan' },
        { status: 404 }
      )
    }
    
    // Get the original document for content
    const document = await Document.findById(studyPlan.documentId)
    if (!document) {
      return NextResponse.json(
        { success: false, message: 'Original document not found' },
        { status: 404 }
      )
    }
    
    // Extract relevant content for this topic (simplified approach)
    const topicIndex = studyPlan.topics.findIndex(t => t.topicId === topicId)
    const contentSections = document.extractedText.split(/\n\s*\n/)
    const topicContent = contentSections[topicIndex] || contentSections[0] || document.extractedText.substring(0, 2000)
    
    // Generate flashcards
    const flashcardData = generateFlashcardsForTopic(topic.title, topicContent, topic.difficulty)
    const createdFlashcards = []
    
    for (const cardData of flashcardData) {
      const flashcard = new Flashcard({
        userId,
        documentId: studyPlan.documentId,
        front: cardData.front,
        back: cardData.back,
        tags: [...cardData.tags, topicId],
        difficulty: cardData.difficulty
      })
      await flashcard.save()
      createdFlashcards.push(flashcard)
    }
    
    // Generate quiz
    const quizQuestions = generateQuizForTopic(topic.title, topicContent, topic.difficulty)
    
    const quiz = new Quiz({
      userId,
      documentId: studyPlan.documentId,
      title: `${topic.title} - Daily Quiz`,
      description: `Quiz for ${topic.title} covering key concepts and understanding`,
      questions: quizQuestions,
      tags: [topicId, topic.title.toLowerCase().replace(/\s+/g, '-')],
      difficulty: topic.difficulty,
      timeLimit: quizQuestions.length * 60, // 1 minute per question
      settings: {
        shuffleQuestions: true,
        shuffleOptions: true,
        showCorrectAnswers: true,
        allowRetakes: true
      }
    })
    await quiz.save()
    
    return NextResponse.json({
      success: true,
      message: 'Study content generated successfully',
      data: {
        topic: {
          id: topic.topicId,
          title: topic.title,
          description: topic.description,
          keyPoints: topic.keyPoints,
          summary: topic.summary,
          motivationTip: topic.motivationTip,
          estimatedDuration: topic.estimatedDuration,
          difficulty: topic.difficulty
        },
        flashcards: createdFlashcards.map(card => ({
          id: card._id,
          front: card.front,
          back: card.back,
          difficulty: card.difficulty
        })),
        quiz: {
          id: quiz._id,
          title: quiz.title,
          description: quiz.description,
          questionCount: quiz.questions.length,
          timeLimit: quiz.timeLimit,
          difficulty: quiz.difficulty
        }
      }
    })
    
  } catch (error) {
    console.error('Content generation error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to generate study content' },
      { status: 500 }
    )
  }
}
