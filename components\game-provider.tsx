"use client"

import { createContext, useContext, useState, type ReactNode } from "react"

interface GameStats {
  level: number
  xp: number
  streak: number
  badges: number
  totalStudyTime: number
  flashcardsReviewed: number
  quizzesCompleted: number
  averageScore: number
}

interface GameContextType {
  userStats: GameStats
  addXP: (amount: number) => void
  incrementStreak: () => void
  resetStreak: () => void
  unlockBadge: () => void
}

const GameContext = createContext<GameContextType | undefined>(undefined)

export function GameProvider({ children }: { children: ReactNode }) {
  const [userStats, setUserStats] = useState<GameStats>({
    level: 5,
    xp: 750,
    streak: 7,
    badges: 3,
    totalStudyTime: 127.5,
    flashcardsReviewed: 1247,
    quizzesCompleted: 45,
    averageScore: 87,
  })

  const addXP = (amount: number) => {
    setUserStats((prev) => {
      const newXP = prev.xp + amount
      const newLevel = Math.floor(newXP / 1000) + 1
      return {
        ...prev,
        xp: newXP,
        level: newLevel,
      }
    })
  }

  const incrementStreak = () => {
    setUserStats((prev) => ({
      ...prev,
      streak: prev.streak + 1,
    }))
  }

  const resetStreak = () => {
    setUserStats((prev) => ({
      ...prev,
      streak: 0,
    }))
  }

  const unlockBadge = () => {
    setUserStats((prev) => ({
      ...prev,
      badges: prev.badges + 1,
    }))
  }

  return (
    <GameContext.Provider value={{ userStats, addXP, incrementStreak, resetStreak, unlockBadge }}>
      {children}
    </GameContext.Provider>
  )
}

export function useGame() {
  const context = useContext(GameContext)
  if (context === undefined) {
    throw new Error("useGame must be used within a GameProvider")
  }
  return context
}
