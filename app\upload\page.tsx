"use client"

import { useState, useCallback } from "react"
import { useRouter } from "next/navigation"
import { Navigation } from "@/components/navigation"
import { ProtectedRoute } from "@/components/protected-route"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, FileText, Brain, Zap, CheckCircle, AlertCircle } from "lucide-react"
import { useDropzone } from "react-dropzone"

interface UploadedFile {
  file: File
  preview: string
}

export default function UploadPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingStep, setProcessingStep] = useState(0)
  const [studyTitle, setStudyTitle] = useState("")
  const [studyDescription, setStudyDescription] = useState("")
  const [difficulty, setDifficulty] = useState("medium")
  const [subject, setSubject] = useState("")
  const [error, setError] = useState("")

  const processingSteps = [
    { name: "Uploading files", description: "Securing your documents" },
    { name: "Extracting content", description: "Reading and analyzing text" },
    { name: "AI processing", description: "Generating summaries and concepts" },
    { name: "Creating flashcards", description: "Building interactive cards" },
    { name: "Generating quizzes", description: "Creating practice questions" },
    { name: "Finalizing", description: "Setting up your study session" },
  ]

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map((file) => ({
      file,
      preview: URL.createObjectURL(file),
    }))
    setUploadedFiles((prev) => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    maxFiles: 5,
  })

  const removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const startProcessing = async () => {
    if (uploadedFiles.length === 0 || !studyTitle || !user) return

    setIsProcessing(true)
    setProcessingStep(0)
    setError("")

    try {
      // Step 1: Upload files
      setProcessingStep(0)
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const formData = new FormData()
      formData.append('file', uploadedFiles[0].file)
      formData.append('userId', user.id)
      formData.append('title', studyTitle)
      formData.append('subject', subject)
      formData.append('difficulty', difficulty)
      formData.append('description', studyDescription)

      // Step 2: Extract content
      setProcessingStep(1)
      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload PDF')
      }

      const uploadData = await uploadResponse.json()

      // Step 3: AI processing
      setProcessingStep(2)
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Step 4: Generate study plan
      setProcessingStep(3)
      const studyPlanResponse = await fetch('/api/study-plans/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: uploadData.data.document.id,
          userId: user.id,
          preferences: {
            dailyStudyTime: 60,
            preferredDays: [1, 2, 3, 4, 5],
            bufferDays: true,
            revisionSlots: true
          }
        }),
      })

      if (!studyPlanResponse.ok) {
        throw new Error('Failed to generate study plan')
      }

      // Step 5: Creating flashcards and quizzes
      setProcessingStep(4)
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Step 6: Finalizing
      setProcessingStep(5)
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Redirect to study dashboard
      router.push("/study/dashboard")
    } catch (error) {
      console.error('Processing error:', error)
      setError(error instanceof Error ? error.message : 'Processing failed')
      setIsProcessing(false)
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navigation />

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Upload Your Study Materials</h1>
          <p className="text-muted-foreground">
            Transform your PDFs into interactive learning experiences with AI-powered tools
          </p>
        </div>

        {!isProcessing ? (
          <div className="space-y-6">
            {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-2 text-red-600">
                    <AlertCircle className="h-5 w-5" />
                    <p>{error}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Upload PDF Files
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary"
                  }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  {isDragActive ? (
                    <p className="text-lg">Drop your PDF files here...</p>
                  ) : (
                    <div>
                      <p className="text-lg mb-2">Drag & drop PDF files here, or click to select</p>
                      <p className="text-sm text-muted-foreground">
                        Support for lecture notes, textbooks, study guides (Max 5 files, 10MB each)
                      </p>
                    </div>
                  )}
                </div>

                {uploadedFiles.length > 0 && (
                  <div className="mt-4 space-y-2">
                    <h4 className="font-medium">Uploaded Files:</h4>
                    {uploadedFiles.map((fileObj, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-red-500" />
                          <div>
                            <p className="font-medium">{fileObj.file.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {(fileObj.file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm" onClick={() => removeFile(index)}>
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Study Session Details */}
            <Card>
              <CardHeader>
                <CardTitle>Study Session Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Study Session Title *</Label>
                  <Input
                    id="title"
                    placeholder="e.g., Biology Chapter 5 - Cell Structure"
                    value={studyTitle}
                    onChange={(e) => setStudyTitle(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    placeholder="Brief description of what you're studying..."
                    value={studyDescription}
                    onChange={(e) => setStudyDescription(e.target.value)}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label>Subject</Label>
                    <Select value={subject} onValueChange={setSubject}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="biology">Biology</SelectItem>
                        <SelectItem value="chemistry">Chemistry</SelectItem>
                        <SelectItem value="physics">Physics</SelectItem>
                        <SelectItem value="mathematics">Mathematics</SelectItem>
                        <SelectItem value="history">History</SelectItem>
                        <SelectItem value="literature">Literature</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Difficulty Level</Label>
                    <Select value={difficulty} onValueChange={setDifficulty}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easy">Easy - Basic recall</SelectItem>
                        <SelectItem value="medium">Medium - Understanding</SelectItem>
                        <SelectItem value="hard">Hard - Application</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* AI Processing Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI Processing Options
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">Generate:</h4>
                    <div className="space-y-2">
                      <label className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">Smart Summary</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">Flashcards</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">Multiple Choice Questions</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">Matching Exercises</span>
                      </label>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Features:</h4>
                    <div className="space-y-2">
                      <label className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">Topic Segmentation</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">Concept Mapping</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">Multilingual Support</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">Audio Generation</span>
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Button
              onClick={startProcessing}
              disabled={uploadedFiles.length === 0 || !studyTitle}
              className="w-full"
              size="lg"
            >
              <Zap className="mr-2 h-5 w-5" />
              Start AI Processing
            </Button>
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Processing Your Study Materials</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-8 w-8 text-primary animate-pulse" />
                </div>
                <h3 className="text-lg font-semibold mb-2">{processingSteps[processingStep].name}</h3>
                <p className="text-muted-foreground">{processingSteps[processingStep].description}</p>
              </div>

              <Progress value={((processingStep + 1) / processingSteps.length) * 100} className="w-full" />

              <div className="space-y-2">
                {processingSteps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3">
                    {index < processingStep ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : index === processingStep ? (
                      <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-muted-foreground" />
                    )}
                    <span className={index <= processingStep ? "text-foreground" : "text-muted-foreground"}>
                      {step.name}
                    </span>
                  </div>
                ))}
              </div>

              <div className="text-center text-sm text-muted-foreground">
                This usually takes 2-3 minutes depending on the size of your documents
              </div>
            </CardContent>
          </Card>
        )}
        </div>
      </div>
    </ProtectedRoute>
  )
}
