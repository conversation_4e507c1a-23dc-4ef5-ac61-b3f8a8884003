// Export all models
export { default as User, type IUser } from './User'
export { default as Document, type IDocument } from './Document'
export { default as Flashcard, type IFlashcard } from './Flashcard'
export { default as Quiz, type IQuiz, type IQuizQuestion, type IQuizAttempt } from './Quiz'

// Re-export database utilities
export { connectToDatabase, disconnectFromDatabase, getDatabase, checkDatabaseHealth } from '../lib/mongodb'
