"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Navigation } from "@/components/navigation"
import { ProtectedRoute } from "@/components/protected-route"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Calendar, 
  Clock, 
  BookOpen, 
  Brain, 
  Target, 
  CheckCircle,
  PlayCircle,
  FileText,
  Lightbulb,
  ArrowLeft,
  Star,
  TrendingUp
} from "lucide-react"
import Link from "next/link"

interface StudyTopic {
  topicId: string
  title: string
  description: string
  estimatedDuration: number
  difficulty: string
  keyPoints: string[]
  summary: string
  motivationTip: string
  isCompleted: boolean
  completedAt?: string
}

interface StudySchedule {
  date: string
  topicId: string
  scheduledDuration: number
  isCompleted: boolean
}

interface StudyPlan {
  id: string
  title: string
  description: string
  totalEstimatedHours: number
  difficulty: string
  subject: string
  topics: StudyTopic[]
  schedule: StudySchedule[]
  progress: {
    completedTopics: number
    totalTopics: number
    completedHours: number
    totalHours: number
    currentStreak: number
  }
  completionPercentage: number
  daysRemaining: number
  estimatedEndDate: string
}

export default function StudyPlanPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [studyPlan, setStudyPlan] = useState<StudyPlan | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedTab, setSelectedTab] = useState("overview")

  useEffect(() => {
    if (user && params.id) {
      fetchStudyPlan()
    }
  }, [user, params.id])

  const fetchStudyPlan = async () => {
    try {
      const response = await fetch(`/api/study-plans?userId=${user?.id}`)
      if (response.ok) {
        const data = await response.json()
        const plan = data.data.find((p: any) => p._id === params.id)
        if (plan) {
          setStudyPlan(plan)
        } else {
          router.push('/documents')
        }
      }
    } catch (error) {
      console.error('Failed to fetch study plan:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const startStudySession = async (topicId: string) => {
    try {
      // Generate flashcards and quiz for this topic
      const response = await fetch('/api/study-plans/generate-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studyPlanId: params.id,
          topicId,
          userId: user?.id
        }),
      })

      if (response.ok) {
        const data = await response.json()
        // Navigate to study session page with the generated content
        router.push(`/study-session/${topicId}?planId=${params.id}`)
      }
    } catch (error) {
      console.error('Failed to start study session:', error)
    }
  }

  const markTopicCompleted = async (topicId: string, actualDuration: number) => {
    try {
      const response = await fetch('/api/study-plans', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studyPlanId: params.id,
          userId: user?.id,
          action: 'complete-topic',
          data: { topicId, actualDuration }
        }),
      })

      if (response.ok) {
        fetchStudyPlan() // Refresh the study plan
      }
    } catch (error) {
      console.error('Failed to mark topic as completed:', error)
    }
  }

  const getTodaysSchedule = () => {
    if (!studyPlan) return []
    const today = new Date().toISOString().split('T')[0]
    return studyPlan.schedule.filter(item => 
      item.date.split('T')[0] === today && !item.isCompleted
    )
  }

  const getUpcomingSchedule = () => {
    if (!studyPlan) return []
    const today = new Date()
    return studyPlan.schedule
      .filter(item => {
        const itemDate = new Date(item.date)
        return itemDate > today && !item.isCompleted
      })
      .slice(0, 7)
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navigation />
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (!studyPlan) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navigation />
          <div className="container mx-auto px-4 py-8">
            <Card>
              <CardContent className="text-center py-12">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Study plan not found</h3>
                <p className="text-muted-foreground mb-4">
                  The study plan you're looking for doesn't exist or has been removed.
                </p>
                <Link href="/documents">
                  <Button>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Documents
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  const todaysSchedule = getTodaysSchedule()
  const upcomingSchedule = getUpcomingSchedule()

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center gap-4 mb-6">
            <Link href="/documents">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">{studyPlan.title}</h1>
              <p className="text-muted-foreground">{studyPlan.description}</p>
            </div>
          </div>

          {/* Progress Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Progress</p>
                    <p className="text-2xl font-bold">{studyPlan.completionPercentage}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-500" />
                </div>
                <Progress value={studyPlan.completionPercentage} className="mt-3" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Topics</p>
                    <p className="text-2xl font-bold">
                      {studyPlan.progress.completedTopics}/{studyPlan.progress.totalTopics}
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Study Time</p>
                    <p className="text-2xl font-bold">
                      {Math.round(studyPlan.progress.completedHours)}h
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Streak</p>
                    <p className="text-2xl font-bold">{studyPlan.progress.currentStreak}</p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="schedule">Schedule</TabsTrigger>
              <TabsTrigger value="topics">All Topics</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Today's Study */}
              {todaysSchedule.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      Today's Study Plan
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {todaysSchedule.map((scheduleItem) => {
                      const topic = studyPlan.topics.find(t => t.topicId === scheduleItem.topicId)
                      if (!topic) return null

                      return (
                        <div key={scheduleItem.topicId} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex-1">
                            <h4 className="font-semibold">{topic.title}</h4>
                            <p className="text-sm text-muted-foreground mb-2">{topic.description}</p>
                            <div className="flex items-center gap-2">
                              <Badge className={getDifficultyColor(topic.difficulty)}>
                                {topic.difficulty}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {scheduleItem.scheduledDuration} minutes
                              </span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button 
                              onClick={() => startStudySession(topic.topicId)}
                              className="flex items-center gap-2"
                            >
                              <PlayCircle className="h-4 w-4" />
                              Start Study
                            </Button>
                          </div>
                        </div>
                      )
                    })}
                  </CardContent>
                </Card>
              )}

              {/* Upcoming Schedule */}
              {upcomingSchedule.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Upcoming Schedule</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {upcomingSchedule.slice(0, 5).map((scheduleItem, index) => {
                        const topic = studyPlan.topics.find(t => t.topicId === scheduleItem.topicId)
                        if (!topic) return null

                        const date = new Date(scheduleItem.date)
                        return (
                          <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                            <div>
                              <p className="font-medium">{topic.title}</p>
                              <p className="text-sm text-muted-foreground">
                                {date.toLocaleDateString()} • {scheduleItem.scheduledDuration} min
                              </p>
                            </div>
                            <Badge className={getDifficultyColor(topic.difficulty)}>
                              {topic.difficulty}
                            </Badge>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="schedule" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Study Schedule</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {studyPlan.schedule.map((scheduleItem, index) => {
                      const topic = studyPlan.topics.find(t => t.topicId === scheduleItem.topicId)
                      if (!topic) return null

                      const date = new Date(scheduleItem.date)
                      const isToday = date.toDateString() === new Date().toDateString()
                      const isPast = date < new Date() && !isToday

                      return (
                        <div 
                          key={index} 
                          className={`flex items-center justify-between p-4 border rounded-lg ${
                            isToday ? 'border-blue-200 bg-blue-50' : 
                            scheduleItem.isCompleted ? 'border-green-200 bg-green-50' :
                            isPast ? 'border-red-200 bg-red-50' : ''
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            {scheduleItem.isCompleted ? (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            ) : (
                              <div className="w-5 h-5 border-2 border-gray-300 rounded-full" />
                            )}
                            <div>
                              <p className="font-medium">{topic.title}</p>
                              <p className="text-sm text-muted-foreground">
                                {date.toLocaleDateString()} • {scheduleItem.scheduledDuration} minutes
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getDifficultyColor(topic.difficulty)}>
                              {topic.difficulty}
                            </Badge>
                            {isToday && !scheduleItem.isCompleted && (
                              <Button 
                                size="sm"
                                onClick={() => startStudySession(topic.topicId)}
                              >
                                <PlayCircle className="h-4 w-4 mr-2" />
                                Study Now
                              </Button>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="topics" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {studyPlan.topics.map((topic) => (
                  <Card key={topic.topicId} className={topic.isCompleted ? 'border-green-200' : ''}>
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center gap-2">
                            {topic.isCompleted && <CheckCircle className="h-5 w-5 text-green-500" />}
                            {topic.title}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground mt-1">{topic.description}</p>
                        </div>
                        <Badge className={getDifficultyColor(topic.difficulty)}>
                          {topic.difficulty}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{topic.estimatedDuration} min</span>
                        </div>
                      </div>

                      {/* Key Points */}
                      <div>
                        <h5 className="font-medium mb-2">Key Points:</h5>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {topic.keyPoints.slice(0, 3).map((point, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                              {point}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Motivation Tip */}
                      <div className="bg-yellow-50 p-3 rounded-lg">
                        <div className="flex items-start gap-2">
                          <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                          <p className="text-sm text-yellow-800">{topic.motivationTip}</p>
                        </div>
                      </div>

                      {/* Action Button */}
                      {!topic.isCompleted && (
                        <Button 
                          onClick={() => startStudySession(topic.topicId)}
                          className="w-full"
                        >
                          <PlayCircle className="h-4 w-4 mr-2" />
                          Start Study Session
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </ProtectedRoute>
  )
}
