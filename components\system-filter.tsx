"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface SystemFilterProps {
  systems: Array<{
    id: string
    name: string
    color: string
    count: number
  }>
  selectedSystem: string
  onSystemChange: (system: string) => void
}

export function SystemFilter({ systems, selectedSystem, onSystemChange }: SystemFilterProps) {
  return (
    <div className="space-y-3">
      <h3 className="font-semibold text-sm">Body Systems</h3>

      <Button
        variant={selectedSystem === "all" ? "default" : "outline"}
        size="sm"
        onClick={() => onSystemChange("all")}
        className="w-full justify-between"
      >
        All Systems
        <Badge variant="secondary">{systems.reduce((sum, system) => sum + system.count, 0)}</Badge>
      </Button>

      <div className="space-y-1">
        {systems.map((system) => (
          <Button
            key={system.id}
            variant={selectedSystem === system.id ? "default" : "ghost"}
            size="sm"
            onClick={() => onSystemChange(system.id)}
            className="w-full justify-between text-left"
          >
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: system.color }} />
              <span className="text-sm">{system.name}</span>
            </div>
            <Badge variant="secondary" className="text-xs">
              {system.count}
            </Badge>
          </Button>
        ))}
      </div>
    </div>
  )
}
