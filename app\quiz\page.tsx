"use client"

import { useState, useEffect } from "react"
import { Navigation } from "@/components/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Check<PERSON>ircle, XCircle, Brain, Clock, Trophy } from "lucide-react"

interface Question {
  id: string
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
  system: string
}

const sampleQuestions: Question[] = [
  {
    id: "1",
    question: "How many chambers does the human heart have?",
    options: ["2", "3", "4", "5"],
    correctAnswer: 2,
    explanation: "The human heart has 4 chambers: 2 atria (left and right) and 2 ventricles (left and right).",
    system: "Circulatory",
  },
  {
    id: "2",
    question: "Which bone is the longest in the human body?",
    options: ["Tibia", "Fe<PERSON>r", "Humerus", "Radius"],
    correctAnswer: 1,
    explanation: "The femur (thighbone) is the longest and strongest bone in the human body.",
    system: "Skeletal",
  },
  {
    id: "3",
    question: "What is the main function of red blood cells?",
    options: ["Fight infections", "Transport oxygen", "Clot blood", "Produce hormones"],
    correctAnswer: 1,
    explanation: "Red blood cells contain hemoglobin, which binds to oxygen and transports it throughout the body.",
    system: "Circulatory",
  },
]

export default function QuizPage() {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState<string>("")
  const [showResult, setShowResult] = useState(false)
  const [score, setScore] = useState(0)
  const [timeLeft, setTimeLeft] = useState(30)
  const [quizStarted, setQuizStarted] = useState(false)
  const [answers, setAnswers] = useState<number[]>([])

  useEffect(() => {
    if (quizStarted && timeLeft > 0 && !showResult) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    } else if (timeLeft === 0 && !showResult) {
      handleNextQuestion()
    }
  }, [timeLeft, quizStarted, showResult])

  const startQuiz = () => {
    setQuizStarted(true)
    setCurrentQuestion(0)
    setScore(0)
    setAnswers([])
    setTimeLeft(30)
  }

  const handleNextQuestion = () => {
    const answerIndex = selectedAnswer ? Number.parseInt(selectedAnswer) : -1
    const newAnswers = [...answers, answerIndex]
    setAnswers(newAnswers)

    if (answerIndex === sampleQuestions[currentQuestion].correctAnswer) {
      setScore(score + 1)
    }

    if (currentQuestion < sampleQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setSelectedAnswer("")
      setTimeLeft(30)
    } else {
      setShowResult(true)
    }
  }

  const resetQuiz = () => {
    setQuizStarted(false)
    setCurrentQuestion(0)
    setSelectedAnswer("")
    setShowResult(false)
    setScore(0)
    setTimeLeft(30)
    setAnswers([])
  }

  if (!quizStarted) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Brain className="h-8 w-8 text-primary" />
              <h1 className="text-3xl font-bold">Quiz Me</h1>
            </div>
            <p className="text-muted-foreground">Test your anatomy knowledge with AI-generated adaptive quizzes</p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Quick Quiz</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Questions:</span>
                    <Badge>{sampleQuestions.length}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Time per question:</span>
                    <Badge variant="secondary">30s</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Difficulty:</span>
                    <Badge variant="outline">Beginner</Badge>
                  </div>
                  <Button onClick={startQuiz} className="w-full">
                    Start Quiz
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Custom Quiz</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Select Systems</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <Badge variant="secondary">Circulatory</Badge>
                      <Badge variant="outline">Skeletal</Badge>
                      <Badge variant="outline">Nervous</Badge>
                      <Badge variant="outline">Respiratory</Badge>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full" disabled>
                    Generate Custom Quiz
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  if (showResult) {
    const percentage = Math.round((score / sampleQuestions.length) * 100)

    return (
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <Card className="text-center">
            <CardHeader>
              <div className="flex items-center justify-center gap-2 mb-4">
                <Trophy className="h-8 w-8 text-yellow-500" />
                <CardTitle className="text-2xl">Quiz Complete!</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-6xl font-bold text-primary">{percentage}%</div>
              <div className="text-lg text-muted-foreground">
                You scored {score} out of {sampleQuestions.length} questions correctly
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Review Your Answers</h3>
                {sampleQuestions.map((question, index) => (
                  <Card key={question.id} className="text-left">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        {answers[index] === question.correctAnswer ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                        )}
                        <div className="flex-1">
                          <p className="font-medium mb-2">{question.question}</p>
                          <p className="text-sm text-muted-foreground mb-2">
                            Correct answer: {question.options[question.correctAnswer]}
                          </p>
                          <p className="text-sm">{question.explanation}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="flex gap-4 justify-center">
                <Button onClick={resetQuiz}>Take Another Quiz</Button>
                <Button variant="outline">Study Weak Areas</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const question = sampleQuestions[currentQuestion]
  const progress = ((currentQuestion + 1) / sampleQuestions.length) * 100

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-muted-foreground">
              Question {currentQuestion + 1} of {sampleQuestions.length}
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span className={`font-mono ${timeLeft <= 10 ? "text-red-500" : ""}`}>{timeLeft}s</span>
            </div>
          </div>
          <Progress value={progress} className="mb-2" />
          <div className="flex justify-between items-center">
            <Badge variant="secondary">{question.system} System</Badge>
            <div className="text-sm text-muted-foreground">
              Score: {score}/{currentQuestion + 1}
            </div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{question.question}</CardTitle>
          </CardHeader>
          <CardContent>
            <RadioGroup value={selectedAnswer} onValueChange={setSelectedAnswer}>
              <div className="space-y-3">
                {question.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                    <Label
                      htmlFor={`option-${index}`}
                      className="flex-1 cursor-pointer p-3 rounded-lg border hover:bg-accent"
                    >
                      {option}
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>

            <div className="flex gap-4 mt-6">
              <Button onClick={handleNextQuestion} disabled={!selectedAnswer} className="flex-1">
                {currentQuestion < sampleQuestions.length - 1 ? "Next Question" : "Finish Quiz"}
              </Button>
              <Button variant="outline" onClick={resetQuiz}>
                Quit Quiz
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
