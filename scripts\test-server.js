#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the Learnify server and API endpoints
 */

const http = require('http')

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = ''
      res.on('data', (chunk) => {
        body += chunk
      })
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body)
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          })
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          })
        }
      })
    })
    
    req.on('error', (error) => {
      reject(error)
    })
    
    if (data) {
      req.write(JSON.stringify(data))
    }
    
    req.end()
  })
}

async function testServer() {
  console.log('🌐 Testing Learnify Server and API Endpoints...\n')
  
  const baseUrl = 'localhost'
  const port = 3000
  
  try {
    // Test 1: Database Test Endpoint
    console.log('1️⃣ Testing database endpoint...')
    const dbTest = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/db/test',
      method: 'GET'
    })
    
    if (dbTest.statusCode === 200 && dbTest.body.success) {
      console.log('   ✅ Database endpoint working')
      console.log('   📊 Collections:', dbTest.body.database.collections.join(', '))
      console.log('   📈 Stats:', JSON.stringify(dbTest.body.database.stats))
    } else {
      console.log('   ❌ Database endpoint failed:', dbTest.statusCode)
      return { success: false, error: 'Database endpoint failed' }
    }
    
    // Test 2: Users API
    console.log('\n2️⃣ Testing users API...')
    
    // GET users
    const getUsers = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/users',
      method: 'GET'
    })
    
    if (getUsers.statusCode === 200) {
      console.log('   ✅ GET /api/users working')
      console.log('   👥 Found users:', getUsers.body.data.length)
    } else {
      console.log('   ❌ GET /api/users failed:', getUsers.statusCode)
    }
    
    // GET user by email
    const getUserByEmail = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/users?email=<EMAIL>',
      method: 'GET'
    })
    
    if (getUserByEmail.statusCode === 200) {
      console.log('   ✅ GET /api/users?email working')
      console.log('   👤 User:', getUserByEmail.body.data.name)
    } else {
      console.log('   ❌ GET /api/users?email failed:', getUserByEmail.statusCode)
    }
    
    // Test 3: Documents API
    console.log('\n3️⃣ Testing documents API...')
    
    const getDocuments = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/documents',
      method: 'GET'
    })
    
    if (getDocuments.statusCode === 200) {
      console.log('   ✅ GET /api/documents working')
      console.log('   📄 Found documents:', getDocuments.body.data.length)
    } else {
      console.log('   ❌ GET /api/documents failed:', getDocuments.statusCode)
    }
    
    // Test 4: Flashcards API
    console.log('\n4️⃣ Testing flashcards API...')
    
    const getFlashcards = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/flashcards',
      method: 'GET'
    })
    
    if (getFlashcards.statusCode === 200) {
      console.log('   ✅ GET /api/flashcards working')
      console.log('   🃏 Found flashcards:', getFlashcards.body.data.length)
    } else {
      console.log('   ❌ GET /api/flashcards failed:', getFlashcards.statusCode)
    }
    
    // Test flashcard review endpoint
    const getFlashcardReview = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/flashcards/review?userId=test',
      method: 'GET'
    })
    
    if (getFlashcardReview.statusCode === 200) {
      console.log('   ✅ GET /api/flashcards/review working')
    } else {
      console.log('   ❌ GET /api/flashcards/review failed:', getFlashcardReview.statusCode)
    }
    
    // Test 5: Quizzes API
    console.log('\n5️⃣ Testing quizzes API...')
    
    const getQuizzes = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/quizzes',
      method: 'GET'
    })
    
    if (getQuizzes.statusCode === 200) {
      console.log('   ✅ GET /api/quizzes working')
      console.log('   📝 Found quizzes:', getQuizzes.body.data.length)
    } else {
      console.log('   ❌ GET /api/quizzes failed:', getQuizzes.statusCode)
    }
    
    // Test 6: Stats API
    console.log('\n6️⃣ Testing stats API...')
    
    const getStats = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/stats?userId=test&type=overview',
      method: 'GET'
    })
    
    if (getStats.statusCode === 200 || getStats.statusCode === 404) {
      console.log('   ✅ GET /api/stats working (expected 404 for test user)')
    } else {
      console.log('   ❌ GET /api/stats failed:', getStats.statusCode)
    }
    
    console.log('\n🎉 All API endpoints tested successfully!')
    console.log('\n📋 API Test Summary:')
    console.log('   ✅ Database test endpoint')
    console.log('   ✅ Users API endpoints')
    console.log('   ✅ Documents API endpoints')
    console.log('   ✅ Flashcards API endpoints')
    console.log('   ✅ Quizzes API endpoints')
    console.log('   ✅ Stats API endpoints')
    
    return { success: true }
    
  } catch (error) {
    console.error('\n❌ Server test failed:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n🔧 Server Connection Issue:')
      console.error('   - Make sure the Next.js server is running on localhost:3000')
      console.error('   - Start the server with: npm run dev')
    }
    
    return { success: false, error: error.message }
  }
}

// Run the test
if (require.main === module) {
  testServer()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ All server tests passed!')
        process.exit(0)
      } else {
        console.log('\n❌ Server tests failed!')
        process.exit(1)
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error)
      process.exit(1)
    })
}

module.exports = { testServer }
