import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Upload, Brain, Play, Trophy } from "lucide-react"

const steps = [
  {
    step: 1,
    icon: Upload,
    title: "Upload Your PDF",
    description: "Simply drag and drop your study materials - lecture notes, textbooks, or any PDF document.",
    color: "bg-pink-300",
  },
  {
    step: 2,
    icon: Brain,
    title: "AI Processing",
    description: "Our advanced AI analyzes your content and extracts key concepts, definitions, and important topics.",
    color: "bg-purple-300",
  },
  {
    step: 3,
    icon: Play,
    title: "Interactive Learning",
    description: "Access auto-generated flashcards, quizzes, summaries, and matching games tailored to your content.",
    color: "bg-green-300",
  },
  {
    step: 4,
    icon: Trophy,
    title: "Track Progress",
    description: "Earn XP, unlock achievements, and monitor your learning progress with detailed analytics.",
    color: "bg-yellow-300",
  },
]

export function HowItWorks() {
  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">How Learnify Works</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Transform your study materials into interactive learning experiences in just four simple steps
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => {
            const Icon = step.icon
            return (
              <Card key={index} className="relative overflow-hidden">
                <CardContent className="p-6 text-center">
                  <div className="relative mb-6">
                    <div
                      className={`w-16 h-16 ${step.color} rounded-full flex items-center justify-center mx-auto mb-4`}
                    >
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <Badge className="absolute -top-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center p-0">
                      {step.step}
                    </Badge>
                  </div>
                  <h3 className="text-lg font-semibold mb-3">{step.title}</h3>
                  <p className="text-muted-foreground text-sm">{step.description}</p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 bg-background rounded-full px-6 py-3 shadow-sm">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium">Processing typically takes 30-60 seconds</span>
          </div>
        </div>
      </div>
    </section>
  )
}
