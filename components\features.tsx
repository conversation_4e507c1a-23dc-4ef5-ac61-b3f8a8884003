import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Upload, Brain, Target, Trophy, Zap, Users, BookOpen, MessageCircle } from "lucide-react"

const features = [
  {
    icon: Upload,
    title: "Smart PDF Processing",
    description: "Upload any PDF and our AI instantly extracts key concepts, definitions, and important information.",
    badge: "AI-Powered",
    color: "text-pink-400",
  },
  {
    icon: Brain,
    title: "Auto-Generated Flashcards",
    description: "Automatically create flashcards from your study materials with term-definition pairs.",
    badge: "Interactive",
    color: "text-purple-400",
  },
  {
    icon: Target,
    title: "Adaptive Quizzes",
    description: "Take AI-generated quizzes that adapt to your learning pace and knowledge level.",
    badge: "Personalized",
    color: "text-green-300",
  },
  {
    icon: Trophy,
    title: "Gamification System",
    description: "Earn XP, unlock achievements, and compete with friends to make learning fun.",
    badge: "Engaging",
    color: "text-yellow-300",
  },
  {
    icon: MessageCircle,
    title: "AI Study Assistant",
    description: "Get instant answers and explanations from your personal AI tutor trained on your materials.",
    badge: "Smart",
    color: "text-rose-400",
  },
  {
    icon: BookOpen,
    title: "Smart Summaries",
    description: "Receive concise, well-structured summaries that highlight the most important concepts.",
    badge: "Efficient",
    color: "text-indigo-300",
  },
  {
    icon: Zap,
    title: "Real-time Progress",
    description: "Track your learning progress with detailed analytics and performance insights.",
    badge: "Analytics",
    color: "text-orange-300",
  },
  {
    icon: Users,
    title: "Collaborative Learning",
    description: "Share study materials and compete with classmates in a social learning environment.",
    badge: "Social",
    color: "text-cyan-300",
  },
]

export function Features() {
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Everything You Need to Excel in Your Studies</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Our comprehensive AI-powered platform transforms traditional studying into an engaging, interactive
            experience
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <Card key={index} className="relative overflow-hidden group hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className={`w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${feature.color}`}
                    >
                      <Icon className="h-5 w-5" />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {feature.badge}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground text-sm">{feature.description}</p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
