import mongoose, { Document, Schema } from 'mongoose'

export interface IFlashcard extends Document {
  userId: string
  documentId: string
  front: string
  back: string
  tags: string[]
  difficulty: 'easy' | 'medium' | 'hard'
  reviewCount: number
  correctCount: number
  lastReviewedAt?: Date
  nextReviewAt: Date
  isActive: boolean
  spacedRepetition: {
    easeFactor: number
    interval: number
    repetitions: number
  }
  createdAt: Date
  updatedAt: Date
}

const FlashcardSchema = new Schema<IFlashcard>({
  userId: {
    type: String,
    required: [true, 'User ID is required']
  },
  documentId: {
    type: String,
    required: [true, 'Document ID is required']
  },
  front: {
    type: String,
    required: [true, 'Front content is required'],
    trim: true,
    maxlength: [500, 'Front content cannot be more than 500 characters']
  },
  back: {
    type: String,
    required: [true, 'Back content is required'],
    trim: true,
    maxlength: [1000, 'Back content cannot be more than 1000 characters']
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  reviewCount: {
    type: Number,
    default: 0,
    min: [0, 'Review count cannot be negative']
  },
  correctCount: {
    type: Number,
    default: 0,
    min: [0, 'Correct count cannot be negative']
  },
  lastReviewedAt: {
    type: Date,
    default: null
  },
  nextReviewAt: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  spacedRepetition: {
    easeFactor: {
      type: Number,
      default: 2.5,
      min: [1.3, 'Ease factor cannot be less than 1.3']
    },
    interval: {
      type: Number,
      default: 1,
      min: [1, 'Interval cannot be less than 1']
    },
    repetitions: {
      type: Number,
      default: 0,
      min: [0, 'Repetitions cannot be negative']
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
FlashcardSchema.index({ userId: 1, nextReviewAt: 1 })
FlashcardSchema.index({ documentId: 1 })
FlashcardSchema.index({ tags: 1 })
FlashcardSchema.index({ difficulty: 1 })
FlashcardSchema.index({ isActive: 1 })

// Virtual for success rate
FlashcardSchema.virtual('successRate').get(function() {
  if (this.reviewCount === 0) return 0
  return Math.round((this.correctCount / this.reviewCount) * 100)
})

// Virtual for days until next review
FlashcardSchema.virtual('daysUntilReview').get(function() {
  const now = new Date()
  const nextReview = new Date(this.nextReviewAt)
  const diffTime = nextReview.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
})

// Virtual for is due for review
FlashcardSchema.virtual('isDue').get(function() {
  return new Date() >= new Date(this.nextReviewAt)
})

// Methods
FlashcardSchema.methods.recordReview = function(isCorrect: boolean, quality: number = 3) {
  this.reviewCount += 1
  this.lastReviewedAt = new Date()
  
  if (isCorrect) {
    this.correctCount += 1
  }
  
  // Update spaced repetition algorithm (SM-2)
  const { easeFactor, interval, repetitions } = this.spacedRepetition
  
  if (quality >= 3) {
    // Correct response
    if (repetitions === 0) {
      this.spacedRepetition.interval = 1
    } else if (repetitions === 1) {
      this.spacedRepetition.interval = 6
    } else {
      this.spacedRepetition.interval = Math.round(interval * easeFactor)
    }
    this.spacedRepetition.repetitions += 1
  } else {
    // Incorrect response
    this.spacedRepetition.repetitions = 0
    this.spacedRepetition.interval = 1
  }
  
  // Update ease factor
  const newEaseFactor = easeFactor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02))
  this.spacedRepetition.easeFactor = Math.max(1.3, newEaseFactor)
  
  // Set next review date
  const nextReviewDate = new Date()
  nextReviewDate.setDate(nextReviewDate.getDate() + this.spacedRepetition.interval)
  this.nextReviewAt = nextReviewDate
  
  return this.save()
}

FlashcardSchema.methods.addTag = function(tag: string) {
  const normalizedTag = tag.toLowerCase().trim()
  if (!this.tags.includes(normalizedTag)) {
    this.tags.push(normalizedTag)
    return this.save()
  }
  return Promise.resolve(this)
}

FlashcardSchema.methods.removeTag = function(tag: string) {
  const normalizedTag = tag.toLowerCase().trim()
  this.tags = this.tags.filter(t => t !== normalizedTag)
  return this.save()
}

FlashcardSchema.methods.resetProgress = function() {
  this.reviewCount = 0
  this.correctCount = 0
  this.lastReviewedAt = null
  this.nextReviewAt = new Date()
  this.spacedRepetition = {
    easeFactor: 2.5,
    interval: 1,
    repetitions: 0
  }
  return this.save()
}

// Static methods
FlashcardSchema.statics.findDueForReview = function(userId: string, limit: number = 20) {
  return this.find({
    userId,
    isActive: true,
    nextReviewAt: { $lte: new Date() }
  })
  .sort({ nextReviewAt: 1 })
  .limit(limit)
}

FlashcardSchema.statics.findByDocument = function(documentId: string, userId?: string) {
  const query: any = { documentId, isActive: true }
  if (userId) query.userId = userId
  return this.find(query).sort({ createdAt: -1 })
}

FlashcardSchema.statics.findByTags = function(tags: string[], userId?: string) {
  const query: any = { 
    tags: { $in: tags.map(tag => tag.toLowerCase()) },
    isActive: true
  }
  if (userId) query.userId = userId
  return this.find(query).sort({ createdAt: -1 })
}

FlashcardSchema.statics.getStudyStats = function(userId: string) {
  return this.aggregate([
    { $match: { userId, isActive: true } },
    {
      $group: {
        _id: null,
        totalCards: { $sum: 1 },
        dueCards: {
          $sum: {
            $cond: [{ $lte: ['$nextReviewAt', new Date()] }, 1, 0]
          }
        },
        averageSuccessRate: {
          $avg: {
            $cond: [
              { $gt: ['$reviewCount', 0] },
              { $multiply: [{ $divide: ['$correctCount', '$reviewCount'] }, 100] },
              0
            ]
          }
        }
      }
    }
  ])
}

// Export the model
export default mongoose.models.Flashcard || mongoose.model<IFlashcard>('Flashcard', FlashcardSchema)
