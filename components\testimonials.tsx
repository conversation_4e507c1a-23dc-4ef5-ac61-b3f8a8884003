import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Star } from "lucide-react"

const testimonials = [
  {
    name: "<PERSON>",
    role: "Medical Student",
    institution: "Harvard Medical School",
    content:
      "Learnify transformed how I study anatomy. The AI-generated flashcards from my textbooks are incredibly accurate and save me hours of manual work.",
    rating: 5,
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    name: "<PERSON>",
    role: "High School Student",
    institution: "Lincoln High School",
    content:
      "The gamification aspect makes studying actually fun! I've improved my grades significantly since using Learnify for my AP Biology class.",
    rating: 5,
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    name: "Dr. <PERSON>",
    role: "Professor",
    institution: "Stanford University",
    content:
      "I use Learnify to create study materials for my students. The AI summaries are remarkably comprehensive and help students focus on key concepts.",
    rating: 5,
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    name: "<PERSON>",
    role: "Graduate Student",
    institution: "MIT",
    content:
      "The AI chat feature is like having a personal tutor available 24/7. It answers my questions based on my uploaded materials with impressive accuracy.",
    rating: 5,
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

export function Testimonials() {
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Loved by Students and Educators</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join thousands of learners who have transformed their study experience with Learnify
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="relative">
              <CardContent className="p-6">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                <blockquote className="text-muted-foreground mb-4">"{testimonial.content}"</blockquote>

                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={testimonial.avatar || "/placeholder.svg"} alt={testimonial.name} />
                    <AvatarFallback>
                      {testimonial.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold">{testimonial.name}</p>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {testimonial.role}
                      </Badge>
                      <span className="text-xs text-muted-foreground">{testimonial.institution}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
