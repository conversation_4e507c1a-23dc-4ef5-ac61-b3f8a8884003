import mongoose, { Document, Schema } from 'mongoose'

export interface IQuizQuestion {
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
  difficulty: 'easy' | 'medium' | 'hard'
}

export interface IQuizAttempt {
  attemptId: string
  startedAt: Date
  completedAt?: Date
  answers: number[]
  score: number
  timeSpent: number
  isCompleted: boolean
}

export interface IQuiz extends Document {
  userId: string
  documentId: string
  title: string
  description: string
  questions: IQuizQuestion[]
  tags: string[]
  difficulty: 'easy' | 'medium' | 'hard'
  timeLimit: number
  attempts: IQuizAttempt[]
  isActive: boolean
  settings: {
    shuffleQuestions: boolean
    shuffleOptions: boolean
    showCorrectAnswers: boolean
    allowRetakes: boolean
  }
  createdAt: Date
  updatedAt: Date
}

const QuizQuestionSchema = new Schema<IQuizQuestion>({
  question: {
    type: String,
    required: [true, 'Question is required'],
    trim: true,
    maxlength: [500, 'Question cannot be more than 500 characters']
  },
  options: [{
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Option cannot be more than 200 characters']
  }],
  correctAnswer: {
    type: Number,
    required: [true, 'Correct answer index is required'],
    min: [0, 'Correct answer index cannot be negative']
  },
  explanation: {
    type: String,
    trim: true,
    maxlength: [1000, 'Explanation cannot be more than 1000 characters']
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  }
}, { _id: false })

const QuizAttemptSchema = new Schema<IQuizAttempt>({
  attemptId: {
    type: String,
    required: true,
    unique: true
  },
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: {
    type: Date
  },
  answers: [{
    type: Number,
    min: [-1, 'Answer index cannot be less than -1'] // -1 for unanswered
  }],
  score: {
    type: Number,
    default: 0,
    min: [0, 'Score cannot be negative'],
    max: [100, 'Score cannot be more than 100']
  },
  timeSpent: {
    type: Number,
    default: 0,
    min: [0, 'Time spent cannot be negative']
  },
  isCompleted: {
    type: Boolean,
    default: false
  }
}, { _id: false })

const QuizSchema = new Schema<IQuiz>({
  userId: {
    type: String,
    required: [true, 'User ID is required']
  },
  documentId: {
    type: String,
    required: [true, 'Document ID is required']
  },
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  questions: {
    type: [QuizQuestionSchema],
    required: [true, 'Questions are required'],
    validate: {
      validator: function(questions: IQuizQuestion[]) {
        return questions.length > 0 && questions.length <= 50
      },
      message: 'Quiz must have between 1 and 50 questions'
    }
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  timeLimit: {
    type: Number,
    default: 300, // 5 minutes in seconds
    min: [60, 'Time limit cannot be less than 60 seconds']
  },
  attempts: [QuizAttemptSchema],
  isActive: {
    type: Boolean,
    default: true
  },
  settings: {
    shuffleQuestions: {
      type: Boolean,
      default: false
    },
    shuffleOptions: {
      type: Boolean,
      default: false
    },
    showCorrectAnswers: {
      type: Boolean,
      default: true
    },
    allowRetakes: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
QuizSchema.index({ userId: 1, createdAt: -1 })
QuizSchema.index({ documentId: 1 })
QuizSchema.index({ tags: 1 })
QuizSchema.index({ difficulty: 1 })
QuizSchema.index({ isActive: 1 })

// Virtual for average score
QuizSchema.virtual('averageScore').get(function() {
  const completedAttempts = this.attempts.filter(attempt => attempt.isCompleted)
  if (completedAttempts.length === 0) return 0
  const totalScore = completedAttempts.reduce((sum, attempt) => sum + attempt.score, 0)
  return Math.round(totalScore / completedAttempts.length)
})

// Virtual for best score
QuizSchema.virtual('bestScore').get(function() {
  const completedAttempts = this.attempts.filter(attempt => attempt.isCompleted)
  if (completedAttempts.length === 0) return 0
  return Math.max(...completedAttempts.map(attempt => attempt.score))
})

// Virtual for total attempts
QuizSchema.virtual('totalAttempts').get(function() {
  return this.attempts.length
})

// Virtual for completion rate
QuizSchema.virtual('completionRate').get(function() {
  if (this.attempts.length === 0) return 0
  const completedAttempts = this.attempts.filter(attempt => attempt.isCompleted).length
  return Math.round((completedAttempts / this.attempts.length) * 100)
})

// Methods
QuizSchema.methods.startAttempt = function(attemptId: string) {
  const newAttempt: IQuizAttempt = {
    attemptId,
    startedAt: new Date(),
    answers: new Array(this.questions.length).fill(-1), // -1 for unanswered
    score: 0,
    timeSpent: 0,
    isCompleted: false
  }
  
  this.attempts.push(newAttempt)
  return this.save()
}

QuizSchema.methods.submitAnswer = function(attemptId: string, questionIndex: number, answerIndex: number) {
  const attempt = this.attempts.find(a => a.attemptId === attemptId)
  if (!attempt || attempt.isCompleted) {
    throw new Error('Invalid attempt or attempt already completed')
  }
  
  if (questionIndex < 0 || questionIndex >= this.questions.length) {
    throw new Error('Invalid question index')
  }
  
  attempt.answers[questionIndex] = answerIndex
  return this.save()
}

QuizSchema.methods.completeAttempt = function(attemptId: string, timeSpent: number) {
  const attempt = this.attempts.find(a => a.attemptId === attemptId)
  if (!attempt) {
    throw new Error('Attempt not found')
  }
  
  // Calculate score
  let correctAnswers = 0
  for (let i = 0; i < this.questions.length; i++) {
    if (attempt.answers[i] === this.questions[i].correctAnswer) {
      correctAnswers++
    }
  }
  
  attempt.score = Math.round((correctAnswers / this.questions.length) * 100)
  attempt.timeSpent = timeSpent
  attempt.completedAt = new Date()
  attempt.isCompleted = true
  
  return this.save()
}

QuizSchema.methods.getAttempt = function(attemptId: string) {
  return this.attempts.find(a => a.attemptId === attemptId)
}

QuizSchema.methods.addTag = function(tag: string) {
  const normalizedTag = tag.toLowerCase().trim()
  if (!this.tags.includes(normalizedTag)) {
    this.tags.push(normalizedTag)
    return this.save()
  }
  return Promise.resolve(this)
}

QuizSchema.methods.removeTag = function(tag: string) {
  const normalizedTag = tag.toLowerCase().trim()
  this.tags = this.tags.filter(t => t !== normalizedTag)
  return this.save()
}

// Static methods
QuizSchema.statics.findByDocument = function(documentId: string, userId?: string) {
  const query: any = { documentId, isActive: true }
  if (userId) query.userId = userId
  return this.find(query).sort({ createdAt: -1 })
}

QuizSchema.statics.findByTags = function(tags: string[], userId?: string) {
  const query: any = { 
    tags: { $in: tags.map(tag => tag.toLowerCase()) },
    isActive: true
  }
  if (userId) query.userId = userId
  return this.find(query).sort({ createdAt: -1 })
}

QuizSchema.statics.getQuizStats = function(userId: string) {
  return this.aggregate([
    { $match: { userId, isActive: true } },
    { $unwind: '$attempts' },
    { $match: { 'attempts.isCompleted': true } },
    {
      $group: {
        _id: null,
        totalQuizzes: { $addToSet: '$_id' },
        totalAttempts: { $sum: 1 },
        averageScore: { $avg: '$attempts.score' },
        bestScore: { $max: '$attempts.score' },
        totalTimeSpent: { $sum: '$attempts.timeSpent' }
      }
    },
    {
      $project: {
        totalQuizzes: { $size: '$totalQuizzes' },
        totalAttempts: 1,
        averageScore: { $round: ['$averageScore', 1] },
        bestScore: 1,
        totalTimeSpent: 1
      }
    }
  ])
}

// Export the model
export default mongoose.models.Quiz || mongoose.model<IQuiz>('Quiz', QuizSchema)
