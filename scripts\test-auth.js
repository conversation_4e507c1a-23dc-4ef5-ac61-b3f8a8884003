#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the authentication system
 */

const http = require('http')

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = ''
      res.on('data', (chunk) => {
        body += chunk
      })
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body)
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          })
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          })
        }
      })
    })
    
    req.on('error', (error) => {
      reject(error)
    })
    
    if (data) {
      req.write(JSON.stringify(data))
    }
    
    req.end()
  })
}

async function testAuth() {
  console.log('🔐 Testing Authentication System...\n')
  
  const baseUrl = 'localhost'
  const port = 3000
  
  try {
    // Test 1: Login with demo user
    console.log('1️⃣ Testing login with demo user...')
    const loginResponse = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      email: '<EMAIL>',
      password: 'demo123'
    })
    
    if (loginResponse.statusCode === 200 && loginResponse.body.success) {
      console.log('   ✅ Login successful!')
      console.log('   👤 User:', loginResponse.body.user.name)
      console.log('   📧 Email:', loginResponse.body.user.email)
      
      // Extract cookie for subsequent requests
      const cookies = loginResponse.headers['set-cookie']
      const authCookie = cookies ? cookies.find(cookie => cookie.startsWith('auth-token=')) : null
      
      if (authCookie) {
        console.log('   🍪 Auth cookie set successfully')
        
        // Test 2: Get user info with cookie
        console.log('\n2️⃣ Testing /api/auth/me with cookie...')
        const meResponse = await makeRequest({
          hostname: baseUrl,
          port: port,
          path: '/api/auth/me',
          method: 'GET',
          headers: {
            'Cookie': authCookie.split(';')[0]
          }
        })
        
        if (meResponse.statusCode === 200 && meResponse.body.success) {
          console.log('   ✅ User info retrieved successfully!')
          console.log('   👤 User:', meResponse.body.user.name)
        } else {
          console.log('   ❌ Failed to get user info:', meResponse.statusCode)
        }
        
        // Test 3: Logout
        console.log('\n3️⃣ Testing logout...')
        const logoutResponse = await makeRequest({
          hostname: baseUrl,
          port: port,
          path: '/api/auth/logout',
          method: 'POST',
          headers: {
            'Cookie': authCookie.split(';')[0]
          }
        })
        
        if (logoutResponse.statusCode === 200 && logoutResponse.body.success) {
          console.log('   ✅ Logout successful!')
        } else {
          console.log('   ❌ Logout failed:', logoutResponse.statusCode)
        }
      } else {
        console.log('   ⚠️ No auth cookie found in response')
      }
    } else {
      console.log('   ❌ Login failed:', loginResponse.statusCode)
      console.log('   📄 Response:', loginResponse.body)
    }
    
    // Test 4: Test invalid login
    console.log('\n4️⃣ Testing invalid login...')
    const invalidLoginResponse = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      email: '<EMAIL>',
      password: 'wrongpassword'
    })
    
    if (invalidLoginResponse.statusCode === 401) {
      console.log('   ✅ Invalid login correctly rejected!')
    } else {
      console.log('   ❌ Invalid login not properly handled:', invalidLoginResponse.statusCode)
    }
    
    // Test 5: Test registration
    console.log('\n5️⃣ Testing user registration...')
    const testEmail = `test${Date.now()}@example.com`
    const registerResponse = await makeRequest({
      hostname: baseUrl,
      port: port,
      path: '/api/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      name: 'Test User',
      email: testEmail,
      password: 'test123'
    })
    
    if (registerResponse.statusCode === 201 && registerResponse.body.success) {
      console.log('   ✅ Registration successful!')
      console.log('   👤 New user:', registerResponse.body.user.name)
    } else {
      console.log('   ❌ Registration failed:', registerResponse.statusCode)
      console.log('   📄 Response:', registerResponse.body)
    }
    
    console.log('\n🎉 Authentication system test completed!')
    console.log('\n📋 Test Summary:')
    console.log('   ✅ Login endpoint')
    console.log('   ✅ User info endpoint')
    console.log('   ✅ Logout endpoint')
    console.log('   ✅ Invalid login handling')
    console.log('   ✅ Registration endpoint')
    
    return { success: true }
    
  } catch (error) {
    console.error('\n❌ Authentication test failed:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n🔧 Server Connection Issue:')
      console.error('   - Make sure the Next.js server is running on localhost:3000')
      console.error('   - Start the server with: npm run dev')
    }
    
    return { success: false, error: error.message }
  }
}

// Run the test
if (require.main === module) {
  testAuth()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ All authentication tests passed!')
        process.exit(0)
      } else {
        console.log('\n❌ Authentication tests failed!')
        process.exit(1)
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error)
      process.exit(1)
    })
}

module.exports = { testAuth }
