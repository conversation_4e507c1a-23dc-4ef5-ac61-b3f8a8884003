import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import StudyPlanModel from '@/models/StudyPlan'

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const documentId = searchParams.get('documentId')
    const active = searchParams.get('active')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = parseInt(searchParams.get('skip') || '0')
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      )
    }
    
    let query: any = { userId }
    
    if (documentId) {
      query.documentId = documentId
    }
    
    if (active === 'true') {
      query.isActive = true
    }
    
    const studyPlans = await StudyPlanModel.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip)
      .populate('documentId', 'title filename subject')
    
    const total = await StudyPlanModel.countDocuments(query)
    
    // Add computed fields
    const enrichedPlans = studyPlans.map(plan => ({
      ...plan.toObject(),
      completionPercentage: plan.completionPercentage,
      daysRemaining: plan.daysRemaining,
      todaysSchedule: plan.getTodaysSchedule(),
      upcomingSchedule: plan.getUpcomingSchedule(3)
    }))
    
    return NextResponse.json({
      success: true,
      data: enrichedPlans,
      pagination: {
        total,
        limit,
        skip,
        hasMore: skip + limit < total
      }
    })
    
  } catch (error) {
    console.error('Error fetching study plans:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to fetch study plans' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const body = await request.json()
    const { studyPlanId, userId, action, data } = body
    
    if (!studyPlanId || !userId || !action) {
      return NextResponse.json(
        { success: false, message: 'Study plan ID, user ID, and action are required' },
        { status: 400 }
      )
    }
    
    const studyPlan = await StudyPlanModel.findById(studyPlanId)
    if (!studyPlan) {
      return NextResponse.json(
        { success: false, message: 'Study plan not found' },
        { status: 404 }
      )
    }
    
    if (studyPlan.userId !== userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized access to study plan' },
        { status: 403 }
      )
    }
    
    switch (action) {
      case 'complete-topic':
        const { topicId, actualDuration } = data
        if (!topicId || !actualDuration) {
          return NextResponse.json(
            { success: false, message: 'Topic ID and actual duration are required' },
            { status: 400 }
          )
        }
        
        await studyPlan.markTopicCompleted(topicId, actualDuration)
        break
        
      case 'update-preferences':
        const { preferences } = data
        if (!preferences) {
          return NextResponse.json(
            { success: false, message: 'Preferences are required' },
            { status: 400 }
          )
        }
        
        studyPlan.preferences = { ...studyPlan.preferences, ...preferences }
        await studyPlan.save()
        break
        
      case 'pause':
        studyPlan.isActive = false
        await studyPlan.save()
        break
        
      case 'resume':
        studyPlan.isActive = true
        await studyPlan.save()
        break
        
      case 'complete':
        studyPlan.isActive = false
        studyPlan.actualEndDate = new Date()
        await studyPlan.save()
        break
        
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        )
    }
    
    return NextResponse.json({
      success: true,
      message: `Study plan ${action} completed successfully`,
      data: {
        studyPlan: {
          ...studyPlan.toObject(),
          completionPercentage: studyPlan.completionPercentage,
          daysRemaining: studyPlan.daysRemaining
        }
      }
    })
    
  } catch (error) {
    console.error('Error updating study plan:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to update study plan' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase()
    
    const { searchParams } = new URL(request.url)
    const studyPlanId = searchParams.get('id')
    const userId = searchParams.get('userId')
    
    if (!studyPlanId || !userId) {
      return NextResponse.json(
        { success: false, message: 'Study plan ID and user ID are required' },
        { status: 400 }
      )
    }
    
    const studyPlan = await StudyPlanModel.findById(studyPlanId)
    if (!studyPlan) {
      return NextResponse.json(
        { success: false, message: 'Study plan not found' },
        { status: 404 }
      )
    }
    
    if (studyPlan.userId !== userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized access to study plan' },
        { status: 403 }
      )
    }
    
    await StudyPlanModel.deleteOne({ _id: studyPlanId })
    
    return NextResponse.json({
      success: true,
      message: 'Study plan deleted successfully'
    })
    
  } catch (error) {
    console.error('Error deleting study plan:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to delete study plan' },
      { status: 500 }
    )
  }
}
