"use client"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { RotateCcw } from "lucide-react"

interface FlashcardProps {
  card: {
    id: string
    front: string
    back: string
    difficulty: "easy" | "medium" | "hard"
    topic: string
    mastered: boolean
  }
  isFlipped: boolean
  onFlip: () => void
}

export function FlashcardComponent({ card, isFlipped, onFlip }: FlashcardProps) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "bg-green-100 text-green-600"
      case "medium":
        return "bg-yellow-100 text-yellow-600"
      case "hard":
        return "bg-pink-100 text-pink-600"
      default:
        return "bg-gray-100 text-gray-600"
    }
  }

  return (
    <div className="perspective-1000">
      <Card
        className={`relative w-full h-80 cursor-pointer transition-transform duration-500 transform-style-preserve-3d ${
          isFlipped ? "rotate-y-180" : ""
        }`}
        onClick={onFlip}
      >
        {/* Front of card */}
        <div className="absolute inset-0 backface-hidden">
          <CardContent className="h-full flex flex-col justify-between p-6">
            <div className="flex justify-between items-start mb-4">
              <Badge variant="outline">{card.topic}</Badge>
              <Badge className={getDifficultyColor(card.difficulty)}>{card.difficulty}</Badge>
            </div>

            <div className="flex-1 flex items-center justify-center text-center">
              <h3 className="text-xl font-semibold leading-relaxed">{card.front}</h3>
            </div>

            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <RotateCcw className="h-4 w-4" />
              <span className="text-sm">Click to reveal answer</span>
            </div>
          </CardContent>
        </div>

        {/* Back of card */}
        <div className="absolute inset-0 backface-hidden rotate-y-180">
          <CardContent className="h-full flex flex-col justify-between p-6 bg-muted/50">
            <div className="flex justify-between items-start mb-4">
              <Badge variant="secondary">Answer</Badge>
              {card.mastered && <Badge className="bg-green-100 text-green-800">Mastered</Badge>}
            </div>

            <div className="flex-1 flex items-center justify-center text-center">
              <p className="text-lg leading-relaxed">{card.back}</p>
            </div>

            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <RotateCcw className="h-4 w-4" />
              <span className="text-sm">Click to flip back</span>
            </div>
          </CardContent>
        </div>
      </Card>
    </div>
  )
}
