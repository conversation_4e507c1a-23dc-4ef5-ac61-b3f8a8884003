import mongoose, { Document, Schema } from 'mongoose'

export interface IStudyTopic {
  topicId: string
  title: string
  description: string
  estimatedDuration: number // in minutes
  difficulty: 'easy' | 'medium' | 'hard'
  prerequisites: string[] // topicIds that should be studied first
  keyPoints: string[]
  summary: string
  motivationTip: string
  pageReferences: number[]
  isCompleted: boolean
  completedAt?: Date
}

export interface IStudySchedule {
  date: Date
  topicId: string
  scheduledDuration: number
  actualDuration?: number
  isCompleted: boolean
  completedAt?: Date
  notes?: string
}

export interface IStudyPlan extends Document {
  userId: string
  documentId: string
  title: string
  description: string
  totalEstimatedHours: number
  difficulty: 'easy' | 'medium' | 'hard'
  subject: string
  topics: IStudyTopic[]
  schedule: IStudySchedule[]
  preferences: {
    dailyStudyTime: number // minutes per day
    preferredDays: number[] // 0-6 (Sunday-Saturday)
    bufferDays: boolean
    revisionSlots: boolean
  }
  progress: {
    completedTopics: number
    totalTopics: number
    completedHours: number
    totalHours: number
    currentStreak: number
    lastStudyDate?: Date
  }
  isActive: boolean
  startDate: Date
  estimatedEndDate: Date
  actualEndDate?: Date
  createdAt: Date
  updatedAt: Date
}

const StudyTopicSchema = new Schema<IStudyTopic>({
  topicId: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Topic title cannot be more than 200 characters']
  },
  description: {
    type: String,
    required: true,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  estimatedDuration: {
    type: Number,
    required: true,
    min: [15, 'Duration must be at least 15 minutes'],
    max: [120, 'Duration cannot exceed 120 minutes']
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  prerequisites: [{
    type: String
  }],
  keyPoints: [{
    type: String,
    maxlength: [500, 'Key point cannot be more than 500 characters']
  }],
  summary: {
    type: String,
    maxlength: [2000, 'Summary cannot be more than 2000 characters']
  },
  motivationTip: {
    type: String,
    maxlength: [500, 'Motivation tip cannot be more than 500 characters']
  },
  pageReferences: [{
    type: Number,
    min: [1, 'Page number must be positive']
  }],
  isCompleted: {
    type: Boolean,
    default: false
  },
  completedAt: {
    type: Date
  }
}, { _id: false })

const StudyScheduleSchema = new Schema<IStudySchedule>({
  date: {
    type: Date,
    required: true
  },
  topicId: {
    type: String,
    required: true
  },
  scheduledDuration: {
    type: Number,
    required: true,
    min: [15, 'Scheduled duration must be at least 15 minutes']
  },
  actualDuration: {
    type: Number,
    min: [0, 'Actual duration cannot be negative']
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  completedAt: {
    type: Date
  },
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot be more than 1000 characters']
  }
}, { _id: false })

const StudyPlanSchema = new Schema<IStudyPlan>({
  userId: {
    type: String,
    required: [true, 'User ID is required']
  },
  documentId: {
    type: String,
    required: [true, 'Document ID is required']
  },
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  totalEstimatedHours: {
    type: Number,
    required: true,
    min: [0.25, 'Total hours must be at least 15 minutes']
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  subject: {
    type: String,
    required: [true, 'Subject is required'],
    trim: true
  },
  topics: [StudyTopicSchema],
  schedule: [StudyScheduleSchema],
  preferences: {
    dailyStudyTime: {
      type: Number,
      default: 60,
      min: [15, 'Daily study time must be at least 15 minutes'],
      max: [480, 'Daily study time cannot exceed 8 hours']
    },
    preferredDays: [{
      type: Number,
      min: [0, 'Day must be 0-6'],
      max: [6, 'Day must be 0-6']
    }],
    bufferDays: {
      type: Boolean,
      default: true
    },
    revisionSlots: {
      type: Boolean,
      default: true
    }
  },
  progress: {
    completedTopics: {
      type: Number,
      default: 0,
      min: [0, 'Completed topics cannot be negative']
    },
    totalTopics: {
      type: Number,
      default: 0,
      min: [0, 'Total topics cannot be negative']
    },
    completedHours: {
      type: Number,
      default: 0,
      min: [0, 'Completed hours cannot be negative']
    },
    totalHours: {
      type: Number,
      default: 0,
      min: [0, 'Total hours cannot be negative']
    },
    currentStreak: {
      type: Number,
      default: 0,
      min: [0, 'Streak cannot be negative']
    },
    lastStudyDate: {
      type: Date
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  estimatedEndDate: {
    type: Date,
    required: true
  },
  actualEndDate: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes
StudyPlanSchema.index({ userId: 1, createdAt: -1 })
StudyPlanSchema.index({ documentId: 1 })
StudyPlanSchema.index({ isActive: 1 })
StudyPlanSchema.index({ 'schedule.date': 1 })

// Virtual for completion percentage
StudyPlanSchema.virtual('completionPercentage').get(function() {
  if (this.progress.totalTopics === 0) return 0
  return Math.round((this.progress.completedTopics / this.progress.totalTopics) * 100)
})

// Virtual for days remaining
StudyPlanSchema.virtual('daysRemaining').get(function() {
  const now = new Date()
  const endDate = new Date(this.estimatedEndDate)
  const diffTime = endDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
})

// Methods
StudyPlanSchema.methods.markTopicCompleted = function(topicId: string, actualDuration: number) {
  const topic = this.topics.find(t => t.topicId === topicId)
  const scheduleItem = this.schedule.find(s => s.topicId === topicId && !s.isCompleted)
  
  if (topic && scheduleItem) {
    topic.isCompleted = true
    topic.completedAt = new Date()
    
    scheduleItem.isCompleted = true
    scheduleItem.completedAt = new Date()
    scheduleItem.actualDuration = actualDuration
    
    this.progress.completedTopics += 1
    this.progress.completedHours += actualDuration / 60
    this.progress.lastStudyDate = new Date()
    
    // Update streak
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (this.progress.lastStudyDate && 
        this.progress.lastStudyDate.toDateString() === yesterday.toDateString()) {
      this.progress.currentStreak += 1
    } else {
      this.progress.currentStreak = 1
    }
  }
  
  return this.save()
}

StudyPlanSchema.methods.getTodaysSchedule = function() {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  return this.schedule.filter(item => {
    const itemDate = new Date(item.date)
    return itemDate >= today && itemDate < tomorrow && !item.isCompleted
  })
}

StudyPlanSchema.methods.getUpcomingSchedule = function(days: number = 7) {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const futureDate = new Date(today)
  futureDate.setDate(futureDate.getDate() + days)
  
  return this.schedule.filter(item => {
    const itemDate = new Date(item.date)
    return itemDate >= today && itemDate < futureDate
  }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
}

// Static methods
StudyPlanSchema.statics.findActiveByUser = function(userId: string) {
  return this.find({ userId, isActive: true }).sort({ createdAt: -1 })
}

StudyPlanSchema.statics.findByDocument = function(documentId: string, userId?: string) {
  const query: any = { documentId }
  if (userId) query.userId = userId
  return this.find(query).sort({ createdAt: -1 })
}

// Export the model
export default mongoose.models.StudyPlan || mongoose.model<IStudyPlan>('StudyPlan', StudyPlanSchema)
