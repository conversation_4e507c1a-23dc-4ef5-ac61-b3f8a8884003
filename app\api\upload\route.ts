import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { Document, User } from '@/models'
import fs from 'fs'
import path from 'path'

// Disable body parsing for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
}

// Helper function to parse multipart form data
async function parseFormData(request: NextRequest): Promise<{ fields: any; files: any }> {
  const formData = await request.formData()
  const fields: any = {}
  const files: any = {}

  for (const [key, value] of formData.entries()) {
    if (value instanceof File) {
      files[key] = value
    } else {
      fields[key] = value
    }
  }

  return { fields, files }
}

// Analyze PDF content and extract topics
function analyzePDFContent(text: string): {
  mainTopics: string[]
  complexity: number
  estimatedStudyTime: number
  suggestedBreakdown: string[]
} {
  const words = text.split(/\s+/).length
  const sentences = text.split(/[.!?]+/).length
  const paragraphs = text.split(/\n\s*\n/).length

  // Extract potential topics (simplified approach)
  const lines = text.split('\n')
  const headings = lines.filter(line => 
    line.trim().length > 0 && 
    line.trim().length < 100 &&
    (line.match(/^[A-Z]/) || line.includes(':') || line.match(/^\d+\./))
  )

  const mainTopics = headings
    .slice(0, 10)
    .map(heading => heading.trim().replace(/^\d+\.?\s*/, ''))
    .filter(topic => topic.length > 5 && topic.length < 80)

  // Calculate complexity based on various factors
  const avgWordsPerSentence = words / sentences
  const avgSentencesPerParagraph = sentences / paragraphs
  const complexityScore = Math.min(10, Math.max(1, 
    (avgWordsPerSentence / 15) * 3 + 
    (avgSentencesPerParagraph / 5) * 2 + 
    (words / 1000) * 2
  ))

  // Estimate study time (words per minute reading + comprehension time)
  const readingTime = words / 200 // 200 words per minute
  const comprehensionTime = readingTime * 0.5 // 50% additional time for comprehension
  const estimatedStudyTime = Math.ceil((readingTime + comprehensionTime) / 60) // in hours

  // Suggest breakdown based on content length and complexity
  const suggestedBreakdown = []
  if (mainTopics.length > 0) {
    const topicsPerSession = Math.max(1, Math.floor(mainTopics.length / Math.ceil(estimatedStudyTime / 1.5)))
    for (let i = 0; i < mainTopics.length; i += topicsPerSession) {
      const sessionTopics = mainTopics.slice(i, i + topicsPerSession)
      suggestedBreakdown.push(`Session ${Math.floor(i / topicsPerSession) + 1}: ${sessionTopics.join(', ')}`)
    }
  }

  return {
    mainTopics,
    complexity: Math.round(complexityScore),
    estimatedStudyTime,
    suggestedBreakdown
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    // Get user ID from auth token (simplified - you might want to use proper auth middleware)
    const authToken = request.cookies.get('auth-token')?.value
    if (!authToken) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse form data
    const { fields, files } = await parseFormData(request)

    const userId = fields.userId
    const title = fields.title
    const subject = fields.subject
    const difficulty = fields.difficulty || 'medium'
    const description = fields.description || ''

    if (!userId || !title || !subject) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Process uploaded file
    const file = files.file
    if (!file) {
      return NextResponse.json(
        { success: false, message: 'No file uploaded' },
        { status: 400 }
      )
    }

    // Validate file type
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { success: false, message: 'Only PDF files are supported' },
        { status: 400 }
      )
    }

    // Read and parse PDF
    const fileBuffer = await file.arrayBuffer()
    const pdf = await import('pdf-parse')
    const pdfData = await pdf.default(Buffer.from(fileBuffer))
    
    // Analyze PDF content
    const analysisData = analyzePDFContent(pdfData.text)

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads')
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true })
    }

    // Save file to permanent location
    const fileName = `${Date.now()}-${file.name}`
    const permanentPath = path.join(uploadsDir, fileName)
    fs.writeFileSync(permanentPath, Buffer.from(fileBuffer))

    // Create document record
    const documentData = {
      userId,
      title,
      filename: file.name,
      fileUrl: `/uploads/${fileName}`,
      fileSize: file.size,
      mimeType: file.type,
      content: pdfData.text.substring(0, 10000), // Store first 10k characters
      extractedText: pdfData.text,
      summary: description,
      tags: [],
      subject,
      difficulty,
      isProcessed: true,
      processingStatus: 'completed',
      metadata: {
        pageCount: pdfData.numpages,
        wordCount: pdfData.text.split(/\s+/).length,
        language: 'en',
        topics: analysisData.mainTopics,
        analysisData
      },
      hasStudyPlan: false
    }

    const document = new Document(documentData)
    await document.save()

    // Update user stats
    await User.findByIdAndUpdate(userId, {
      $inc: { 'stats.documentsUploaded': 1 }
    })

    return NextResponse.json({
      success: true,
      message: 'PDF uploaded and analyzed successfully',
      data: {
        document: {
          id: document._id,
          title: document.title,
          filename: document.filename,
          fileSize: document.fileSize,
          pageCount: document.metadata?.pageCount,
          wordCount: document.metadata?.wordCount,
          analysisData: document.metadata?.analysisData,
          createdAt: document.createdAt
        }
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to upload and process PDF' },
      { status: 500 }
    )
  }
}
