"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, Volume2 } from "lucide-react"

export function Demo() {
  const [isPlaying, setIsPlaying] = useState(false)

  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">See Bolt AI in Action</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Watch how students are revolutionizing their anatomy learning experience
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-video bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center">
                <div className="absolute inset-0 bg-black/20" />

                {/* Demo Video Placeholder */}
                <div className="relative z-10 text-center text-white">
                  <div className="mb-6">
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                      {isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8 ml-1" />}
                    </div>
                    <h3 className="text-2xl font-bold mb-2">Interactive Demo</h3>
                    <p className="text-white/80 max-w-md mx-auto">
                      Experience 3D anatomy exploration, AR visualization, and AI-powered learning
                    </p>
                  </div>

                  <Button
                    size="lg"
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="bg-white text-black hover:bg-white/90"
                  >
                    {isPlaying ? "Pause Demo" : "Play Demo"}
                  </Button>
                </div>

                {/* Demo Features Overlay */}
                <div className="absolute top-4 left-4">
                  <Badge className="bg-white/20 text-white backdrop-blur-sm">
                    <Volume2 className="h-3 w-3 mr-1" />
                    Audio Enabled
                  </Badge>
                </div>

                <div className="absolute bottom-4 right-4">
                  <Badge className="bg-white/20 text-white backdrop-blur-sm">3:42 Duration</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-3 gap-4 mt-8">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">3D</div>
                <div className="text-sm text-muted-foreground">Interactive Models</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">AR</div>
                <div className="text-sm text-muted-foreground">Augmented Reality</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">AI</div>
                <div className="text-sm text-muted-foreground">Smart Assistant</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
