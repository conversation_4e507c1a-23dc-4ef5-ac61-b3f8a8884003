"use client"

import { useState, useRef } from "react"
import { Navigation } from "@/components/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Camera, Square, RotateCw, Volume2, VolumeX } from "lucide-react"

export default function ARViewerPage() {
  const [isARActive, setIsARActive] = useState(false)
  const [selectedModel, setSelectedModel] = useState("heart")
  const [audioEnabled, setAudioEnabled] = useState(true)
  const videoRef = useRef<HTMLVideoElement>(null)

  const anatomyModels = [
    { id: "heart", name: "Human Heart", system: "Circulatory" },
    { id: "brain", name: "Human Brain", system: "Nervous" },
    { id: "lungs", name: "Lungs", system: "Respiratory" },
    { id: "skeleton", name: "Skeleton", system: "Skeletal" },
  ]

  const startAR = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" },
      })
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        setIsARActive(true)
      }
    } catch (error) {
      console.error("Error accessing camera:", error)
      alert("Camera access is required for AR functionality")
    }
  }

  const stopAR = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach((track) => track.stop())
      videoRef.current.srcObject = null
    }
    setIsARActive(false)
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">AR Anatomy Viewer</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Experience anatomy in augmented reality. Place 3D models in your real environment and interact with them
            using touch gestures.
          </p>
        </div>

        {!isARActive ? (
          <div className="max-w-4xl mx-auto">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  Launch AR Experience
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">Select Anatomy Model</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {anatomyModels.map((model) => (
                        <Button
                          key={model.id}
                          variant={selectedModel === model.id ? "default" : "outline"}
                          className="h-auto p-3 flex flex-col items-start"
                          onClick={() => setSelectedModel(model.id)}
                        >
                          <div className="font-medium">{model.name}</div>
                          <Badge variant="secondary" className="mt-1">
                            {model.system}
                          </Badge>
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-3">AR Instructions</h3>
                    <ul className="text-sm text-muted-foreground space-y-2">
                      <li>• Point your camera at a flat surface</li>
                      <li>• Tap to place the 3D model</li>
                      <li>• Pinch to scale the model</li>
                      <li>• Drag to rotate and move</li>
                      <li>• Tap organs for audio descriptions</li>
                    </ul>
                  </div>
                </div>

                <Button onClick={startAR} className="w-full mt-6" size="lg">
                  <Camera className="mr-2 h-5 w-5" />
                  Launch AR Camera
                </Button>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="relative w-full h-[70vh] bg-black rounded-lg overflow-hidden">
            <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover" />

            {/* AR Overlay */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-32 h-32 border-2 border-white border-dashed rounded-lg flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="text-sm">Tap to place</div>
                    <div className="font-semibold">{anatomyModels.find((m) => m.id === selectedModel)?.name}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* AR Controls */}
            <div className="absolute top-4 left-4 right-4 flex justify-between pointer-events-auto">
              <Card className="p-2">
                <div className="flex items-center gap-2">
                  <Badge>{anatomyModels.find((m) => m.id === selectedModel)?.name}</Badge>
                </div>
              </Card>

              <div className="flex gap-2">
                <Button variant="secondary" size="sm" onClick={() => setAudioEnabled(!audioEnabled)}>
                  {audioEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                </Button>
                <Button variant="secondary" size="sm">
                  <RotateCw className="h-4 w-4" />
                </Button>
                <Button variant="secondary" size="sm">
                  <Square className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="absolute bottom-4 left-4 right-4 pointer-events-auto">
              <div className="flex justify-center gap-4">
                <Button onClick={stopAR} variant="destructive">
                  Stop AR
                </Button>
                <Button variant="secondary">Switch Model</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
